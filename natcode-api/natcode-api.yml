openapi: 3.1.1

info:
  version: 0.1.0
  title: NatCode Assistant
  description: Backend API for AI-powered chat application with user management and chat history

servers:
  - url: http://localhost:8080
    description: Local machine
tags:
  - name: "Users"
    description: User management and profile methods
  - name: "Chats"
    description: Chat session management methods
  - name: "Messages"
    description: Message management methods

paths:
  /api/users/me:
    get:
      summary: Get current user profile information
      description: Retrieve profile information for the authenticated user including username, role, and user ID
      tags:
        - "Users"
      operationId: getUserProfile
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/UserProfile"
              examples:
                success:
                  $ref: "#/components/examples/UserProfileSuccess"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/chats:
    get:
      summary: Get all chat sessions for the user
      description: Retrieve a paginated list of all chat sessions belonging to the authenticated user
      tags:
        - "Chats"
      operationId: getUserChats
      parameters:
        - $ref: "#/components/parameters/page"
        - $ref: "#/components/parameters/size"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChatListResponse"
              examples:
                normalList:
                  $ref: "#/components/examples/ChatListNormal"
                emptyList:
                  $ref: "#/components/examples/ChatListEmpty"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "500":
          $ref: "#/components/responses/InternalServerError"
    post:
      summary: Start a new chat session
      description: Create a new chat session with the specified AI provider
      tags:
        - "Chats"
      operationId: createChat
      requestBody:
        description: Chat session creation request
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/CreateChatRequest"
            examples:
              openai:
                $ref: "#/components/examples/OpenaiProvider"
              azureOpenai:
                $ref: "#/components/examples/AzureOpenaiProvider"
              googleGemini:
                $ref: "#/components/examples/GoogleGeminiProvider"
      responses:
        "201":
          description: "Created"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChatResponse"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/chats/{chatId}:
    get:
      summary: Get a specific chat session with paginated message history by ID
      description: Retrieve a specific chat session and its paginated message history
      tags:
        - "Chats"
      operationId: getChatById
      parameters:
        - name: chatId
          in: path
          required: true
          description: The unique identifier of the chat session
          schema:
            type: string
          example: "456e7890-e12b-34d5-a678-************"
        - $ref: "#/components/parameters/page"
        - $ref: "#/components/parameters/size"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/ChatWithMessagesResponse"
              examples:
                withMessages:
                  $ref: "#/components/examples/ChatWithMessages"
                noMessages:
                  $ref: "#/components/examples/ChatNoMessages"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"
    delete:
      summary: Delete a chat session by ID
      description: Permanently delete a chat session and all its messages
      tags:
        - "Chats"
      operationId: deleteChatById
      parameters:
        - name: chatId
          in: path
          required: true
          description: The unique identifier of the chat session
          schema:
            type: string
          example: "456e7890-e12b-34d5-a678-************"
      responses:
        "204":
          description: "No Content"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"

  /api/chats/{chatId}/messages:
    post:
      summary: Send a message to the AI assistant
      description: Send a text message to the AI assistant and receive a response
      tags:
        - "Messages"
      operationId: sendMessage
      parameters:
        - name: chatId
          in: path
          required: true
          description: The unique identifier of the chat session
          schema:
            type: string
          example: "456e7890-e12b-34d5-a678-************"
      requestBody:
        description: Message to send to the AI assistant
        required: true
        content:
          application/json:
            schema:
              $ref: "#/components/schemas/SendMessageRequest"
            example:
              content: "Hello, can you help me understand how machine learning works?"
              provider: "azure_openai"
      responses:
        "200":
          description: "OK"
          content:
            application/json:
              schema:
                $ref: "#/components/schemas/SendMessageResponse"
              examples:
                success:
                  $ref: "#/components/examples/SendMessageResponseSuccess"
        "400":
          $ref: "#/components/responses/BadRequest"
        "401":
          $ref: "#/components/responses/Unauthorized"
        "403":
          $ref: "#/components/responses/Forbidden"
        "404":
          $ref: "#/components/responses/NotFound"
        "500":
          $ref: "#/components/responses/InternalServerError"

components:
  parameters:
    page:
      name: page
      in: query
      description: The page number for pagination
      required: false
      schema:
        type: integer
        minimum: 1
        default: 1
    size:
      name: size
      in: query
      description: The page size
      required: false
      schema:
        type: integer
        minimum: 1
        default: 5
  examples:
    OpenaiProvider:
      summary: Create chat with OpenAI provider
      value:
        provider: "openai"
    AzureOpenaiProvider:
      summary: Create chat with Azure OpenAI provider
      value:
        provider: "azure_openai"
    GoogleGeminiProvider:
      summary: Create chat with Google Gemini provider
      value:
        provider: "google_gemini"
    UserProfileSuccess:
      summary: Successful user profile response
      value:
        userId: "123e4567-e89b-12d3-a456-************"
        username: "john.doe"
        role: "basic_user"
        createdAt: "2024-01-15T10:30:00Z"
    ChatSuccess:
      summary: Successful chat creation response
      value:
        chatId: "456e7890-e12b-34d5-a678-************"
        provider: "azure_openai"
        userId: "123e4567-e89b-12d3-a456-************"
        createdAt: "2024-01-15T10:30:00Z"
        updatedAt: "2024-01-15T11:45:30Z"
    ChatListNormal:
      summary: Paginated response with multiple chats
      value:
        totalItems: 2
        totalPages: 1
        currentPage: 1
        chats:
          - chatId: "456e7890-e12b-34d5-a678-************"
            title: "Machine Learning Discussion"
            provider: "azure_openai"
            userId: "123e4567-e89b-12d3-a456-************"
            createdAt: "2024-01-15T10:30:00Z"
            updatedAt: "2024-01-15T11:45:30Z"
          - chatId: "789f0123-f45c-67e8-b901-234567890abc"
            title: "Python Programming Help"
            provider: "google_gemini"
            userId: "123e4567-e89b-12d3-a456-************"
            createdAt: "2024-01-14T09:15:22Z"
            updatedAt: "2024-01-14T10:30:45Z"
    ChatListEmpty:
      summary: Paginated empty chat list for new user
      value:
        totalItems: 0
        totalPages: 0
        currentPage: 1
        chats: []
    ChatWithMessages:
      summary: Paginated chat with message history
      value:
        totalItems: 2
        totalPages: 1
        currentPage: 1
        chat:
          chatId: "456e7890-e12b-34d5-a678-************"
          title: "Machine Learning Discussion"
          provider: "azure_openai"
          userId: "123e4567-e89b-12d3-a456-************"
          createdAt: "2024-01-15T10:30:00Z"
          updatedAt: "2024-01-15T11:45:30Z"
        messages:
          - messageId: "abc12345-def6-7890-ghij-klmnopqrstuv"
            chatId: "456e7890-e12b-34d5-a678-************"
            content: "Hello, can you help me understand how machine learning works?"
            sender: "user"
            timestamp: "2024-01-15T10:35:00Z"
          - messageId: "xyz67890-abc1-2345-defg-hijklmnopqrs"
            chatId: "456e7890-e12b-34d5-a678-************"
            content: "I'd be happy to help! Machine learning is a branch of artificial intelligence that focuses on building systems that can learn and improve from data without being explicitly programmed for every task."
            sender: "assistant"
            timestamp: "2024-01-15T10:35:15Z"
    ChatNoMessages:
      summary: Paginated new chat with no messages yet
      value:
        totalItems: 0
        totalPages: 0
        currentPage: 1
        chat:
          chatId: "456e7890-e12b-34d5-a678-************"
          title: "New Chat Session"
          provider: "azure_openai"
          userId: "123e4567-e89b-12d3-a456-************"
          createdAt: "2024-01-15T10:30:00Z"
          updatedAt: "2024-01-15T10:30:00Z"
        messages: []
    SendMessageResponseSuccess:
      summary: Successful message exchange
      value:
        userMessage:
          messageId: "abc12345-def6-7890-ghij-klmnopqrstuv"
          chatId: "456e7890-e12b-34d5-a678-************"
          content: "Hello, can you help me understand how machine learning works?"
          sender: "user"
          timestamp: "2024-01-15T10:35:00Z"
        assistantResponse:
          messageId: "xyz67890-abc1-2345-defg-hijklmnopqrs"
          chatId: "456e7890-e12b-34d5-a678-************"
          content: "I'd be happy to help! Machine learning is a branch of artificial intelligence that focuses on building systems that can learn and improve from data without being explicitly programmed for every task."
          sender: "assistant"
          timestamp: "2024-01-15T10:35:15Z"
  schemas:
    Provider:
      type: string
      enum: [openai, azure_openai, google_gemini]
      description: Available AI providers
      example: "azure_openai"
    ErrorResponse:
      description: This is the response object in case of errors
      type: object
      required:
        - error
      properties:
        error:
          $ref: "#/components/schemas/ErrorData"
    ErrorData:
      type: object
      required:
        - type
        - title
        - status
      properties:
        type:
          type: string
          format: uri
          description: A URI reference that identifies the problem type
          example: "https://example.com/errors/validation-error"
        title:
          type: string
          description: A short, human-readable summary of the problem type
          example: "Validation Error"
        status:
          type: integer
          minimum: 100
          maximum: 599
          description: The HTTP status code
          example: 400
        detail:
          type: string
          description: A human-readable explanation specific to this occurrence
          example: "The request body contains invalid data"
    UserProfile:
      description: User profile information
      type: object
      required:
        - userId
        - username
        - role
        - createdAt
      properties:
        userId:
          type: string
          description: Unique identifier for the user
          example: "123e4567-e89b-12d3-a456-************"
        username:
          type: string
          minLength: 3
          maxLength: 50
          pattern: "^[a-zA-Z0-9._-]+$"
          description: The user's unique username
          example: "john.doe"
        role:
          type: string
          enum: [basic_user, admin, moderator]
          description: The user's role in the system
          example: "basic_user"
        createdAt:
          type: string
          format: date-time
          description: When the user account was created
          example: "2024-01-15T10:30:00Z"
    CreateChatRequest:
      description: Request to create a new chat session
      type: object
      required:
        - provider
      properties:
        provider:
          $ref: "#/components/schemas/Provider"
          description: The AI provider to use for this chat session
    ChatResponse:
      description: Chat session information
      type: object
      required:
        - chatId
        - title
        - provider
        - userId
        - createdAt
        - updatedAt
      properties:
        chatId:
          type: string
          description: Unique identifier for the chat session
          example: "456e7890-e12b-34d5-a678-************"
        title:
          type: string
          minLength: 1
          maxLength: 100
          description: The chat session title
          example: "Machine Learning Discussion"
        provider:
          $ref: "#/components/schemas/Provider"
          description: The AI provider used for this session
        userId:
          type: string
          description: ID of the user who owns this chat
          example: "123e4567-e89b-12d3-a456-************"
        createdAt:
          type: string
          format: date-time
          description: When the chat session was created
          example: "2024-01-15T10:30:00Z"
        updatedAt:
          type: string
          format: date-time
          description: When the chat session was last updated
          example: "2024-01-15T11:45:30Z"
    MessageResponse:
      description: Chat message
      type: object
      required:
        - messageId
        - chatId
        - content
        - sender
        - timestamp
      properties:
        messageId:
          type: string
          description: Unique identifier for the message
          example: "abc12345-def6-7890-ghij-klmnopqrstuv"
        chatId:
          type: string
          description: ID of the chat session this message belongs to
          example: "456e7890-e12b-34d5-a678-************"
        content:
          type: string
          minLength: 1
          maxLength: 8000
          description: The message content
          example: "Hello, can you help me understand how machine learning works?"
        sender:
          type: string
          enum: [user, assistant]
          description: Who sent the message
          example: "user"
        timestamp:
          type: string
          format: date-time
          description: When the message was sent
          example: "2024-01-15T10:35:00Z"
    ChatListResponse:
      type: object
      properties:
        totalItems:
          type: integer
          description: Total number of chats
        totalPages:
          type: integer
          description: Total number of pages
        currentPage:
          type: integer
          description: Current page number
        chats:
          type: array
          items:
            $ref: "#/components/schemas/ChatResponse"
          description: Paginated list of chat sessions
    ChatWithMessagesResponse:
      type: object
      properties:
        totalItems:
          type: integer
          description: Total number of messages in the chat
        totalPages:
          type: integer
          description: Total number of pages
        currentPage:
          type: integer
          description: Current page number
        chat:
          $ref: "#/components/schemas/ChatResponse"
          description: Chat session information
        messages:
          type: array
          items:
            $ref: "#/components/schemas/MessageResponse"
          description: Paginated list of messages for this chat session
    SendMessageRequest:
      description: Request to send a message
      type: object
      required:
        - content
        - provider
      properties:
        content:
          type: string
          minLength: 1
          maxLength: 8000
          description: The message content to send to the AI assistant
          example: "Hello, can you help me understand how machine learning works?"
        provider:
          $ref: "#/components/schemas/Provider"
          description: The AI provider to use for this message
    SendMessageResponse:
      description: Response containing both the user message and assistant response
      type: object
      required:
        - userMessage
        - assistantResponse
      properties:
        userMessage:
          $ref: "#/components/schemas/MessageResponse"
          description: The original user message that was sent
        assistantResponse:
          $ref: "#/components/schemas/MessageResponse"
          description: The AI assistant's response to the user message
  responses:
    BadRequest:
      description: "BAD REQUEST"
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          examples:
            validation_error:
              summary: Validation error with field details
              value:
                error:
                  type: "https://example.com/errors/validation-error"
                  title: "Validation Error"
                  status: 400
                  detail: "The request contains invalid data"
            content_too_long:
              summary: Message content exceeds maximum length
              value:
                error:
                  type: "https://example.com/errors/validation-error"
                  title: "Content Too Long"
                  status: 400
                  detail: "Message content exceeds the maximum allowed length of 8000 characters"
    Unauthorized:
      description: "UNAUTHORIZED"
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          examples:
            missing_token:
              summary: Missing authentication token
              value:
                error:
                  type: "https://example.com/errors/authentication-required"
                  title: "Authentication Required"
                  status: 401
                  detail: "This endpoint requires a valid Bearer token"
            invalid_token:
              summary: Invalid or expired token
              value:
                error:
                  type: "https://example.com/errors/invalid-token"
                  title: "Invalid Token"
                  status: 401
                  detail: "The provided authentication token is invalid or has expired"
    Forbidden:
      description: "FORBIDDEN"
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          examples:
            access_denied:
              summary: User doesn't own the resource
              value:
                error:
                  type: "https://example.com/errors/access-denied"
                  title: "Access Denied"
                  status: 403
                  detail: "You don't have permission to access this chat session"
            insufficient_permissions:
              summary: User role lacks required permissions
              value:
                error:
                  type: "https://example.com/errors/insufficient-permissions"
                  title: "Insufficient Permissions"
                  status: 403
                  detail: "Your current role does not allow this operation"
    NotFound:
      description: "NOT FOUND"
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          examples:
            chat_not_found:
              summary: Chat session not found
              value:
                error:
                  type: "https://example.com/errors/resource-not-found"
                  title: "Chat Not Found"
                  status: 404
                  detail: "The specified chat session does not exist or has been deleted"
            message_not_found:
              summary: Message not found
              value:
                error:
                  type: "https://example.com/errors/resource-not-found"
                  title: "Message Not Found"
                  status: 404
                  detail: "The specified message does not exist in this chat session"
    InternalServerError:
      description: "INTERNAL SERVER ERROR"
      content:
        application/json:
          schema:
            $ref: "#/components/schemas/ErrorResponse"
          examples:
            server_error:
              summary: Generic server error
              value:
                error:
                  type: "https://example.com/errors/internal-server-error"
                  title: "Internal Server Error"
                  status: 500
                  detail: "An unexpected error occurred while processing your request"
  securitySchemes:
    BearerAuth:
      type: http
      scheme: bearer
      bearerFormat: JWT
security: 
  - BearerAuth: [ ]
