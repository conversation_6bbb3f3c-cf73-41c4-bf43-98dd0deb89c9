import {
  AuthServiceFactory,
  ServiceType,
  type KeycloakServiceDefault,
  type KeycloakConfigDefault,
} from 'virava';

export const authService = AuthServiceFactory.create(ServiceType.DEFAULT) as KeycloakServiceDefault;

export const authConfig: KeycloakConfigDefault = {
  clientId: import.meta.env.VITE_KEYCLOAK_CLIENT_ID,
  baseUrl: import.meta.env.VITE_KEYCLOAK_BASE_URL,
  realm: import.meta.env.VITE_KEYCLOAK_REALM,
};

export const extractAccessToken = (): string | null => {
  try {
    return authService.getAccessTokenRaw();
  } catch {
    return null;
  }
};

export const extractUsername = (): string | null => {
  try {
    const token = authService.getAccessToken<{
      preferred_username?: string;
      name?: string;
      sub?: string;
    }>();
    return token?.preferred_username || token?.name || token?.sub || null;
  } catch {
    return null;
  }
};
