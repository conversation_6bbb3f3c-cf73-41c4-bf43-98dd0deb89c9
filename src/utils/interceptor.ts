import { CODE_FORBIDDEN, CODE_UNAUTHORIZED } from '@/constants/api';
import { ROUTES } from '@/constants/routes';
import { authService } from '@/utils/virava';

// Import the OpenAPI client to set global headers
import { client } from '@/lib/api/client.gen';

const setAuthHeader = (
  headers: Record<string, string> = {},
  token: string | null,
): Record<string, string> => {
  return token ? { ...headers, Authorization: `Bearer ${token}` } : headers;
};

export async function interceptRequest<Type>(
  serviceFunc: (...args: unknown[]) => Promise<Type>,
  options: {
    headers?: Record<string, string>;
    getAccessToken: () => string | null;
    onLogout: () => Promise<void>;
    navigate: (path: string) => void;
  },
  ...args: unknown[]
): Promise<Type> {
  try {
    // Set global headers on the OpenAPI client
    const headers = setAuthHeader(options.headers, options.getAccessToken());
    if (client.setConfig) {
      client.setConfig({ headers });
    }

    return await serviceFunc(...args, options);
  } catch (error) {
    const status = (error as { status: number })?.status;

    if (status === CODE_UNAUTHORIZED) {
      try {
        console.warn('Token expired. Attempting refresh...');

        await authService.updateToken();
        const newToken = options.getAccessToken();

        if (newToken) {
          const newHeaders = setAuthHeader(options.headers, newToken);
          if (client.setConfig) {
            client.setConfig({ headers: newHeaders });
          }
          return await serviceFunc(...args, options);
        }

        console.error('Token refresh failed. Logging out...');
        await options.onLogout();
        throw new Error('Authentication expired. Please log in again.');
      } catch {
        await options.onLogout();
        throw new Error('Session expired. Redirecting to login...');
      }
    }

    if (status === CODE_FORBIDDEN) {
      console.error('Forbidden request. Redirecting to home...');
      options.navigate(ROUTES.ROOT);
      throw new Error('You do not have permission to perform this action.');
    }

    console.error('Unhandled API error:', error);
    throw new Error('Something went wrong. Please try again later.');
  }
}
