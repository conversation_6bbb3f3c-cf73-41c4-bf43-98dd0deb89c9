import type {
  UserPro<PERSON>le,
  ChatListResponse,
  ChatWithMessagesResponse,
  Chat,
  CreateChatRequest,
  SendMessageRequest,
  MessageResponse,
} from '@/types/api';

const API_BASE_URL = 'http://localhost:8080/api';

class ApiService {
  private token: string | null = null;

  setToken(token: string) {
    this.token = token;
  }

  private async request<T>(endpoint: string, options: RequestInit = {}): Promise<T> {
    const url = `${API_BASE_URL}${endpoint}`;
    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string>),
    };

    if (this.token) {
      headers['Authorization'] = `Bearer ${this.token}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({
        error: {
          type: 'unknown_error',
          title: 'Unknown Error',
          status: response.status,
          detail: `Request failed with status ${response.status}`,
        },
      }));
      throw new Error(errorData.error?.detail || `HTTP ${response.status}`);
    }

    return response.json();
  }

  // User endpoints
  async getUserProfile(): Promise<UserProfile> {
    return this.request<UserProfile>('/users/me');
  }

  // Chat endpoints
  async getUserChats(page = 1, size = 20): Promise<ChatListResponse> {
    return this.request<ChatListResponse>(`/chats?page=${page}&size=${size}`);
  }

  async createChat(request: CreateChatRequest): Promise<Chat> {
    return this.request<Chat>('/chats', {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }

  async getChatById(chatId: string, page = 1, size = 50): Promise<ChatWithMessagesResponse> {
    return this.request<ChatWithMessagesResponse>(`/chats/${chatId}?page=${page}&size=${size}`);
  }

  async deleteChatById(chatId: string): Promise<void> {
    return this.request<void>(`/chats/${chatId}`, {
      method: 'DELETE',
    });
  }

  // Message endpoints
  async sendMessage(chatId: string, request: SendMessageRequest): Promise<MessageResponse> {
    return this.request<MessageResponse>(`/chats/${chatId}/messages`, {
      method: 'POST',
      body: JSON.stringify(request),
    });
  }
}

export const apiService = new ApiService();

// For development/testing - mock mode
export class MockApiService {
  private mockChats: Chat[] = [
    {
      chatId: '1',
      title: 'Assistant Introduction',
      provider: 'azure_openai',
      userId: 'user1',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
    {
      chatId: '2',
      title: 'Count Filled Alphanum...',
      provider: 'openai',
      userId: 'user1',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    },
  ];

  async getUserProfile(): Promise<UserProfile> {
    return {
      userId: 'user1',
      username: 'john.doe',
      role: 'basic_user',
      createdAt: new Date().toISOString(),
    };
  }

  async getUserChats(): Promise<ChatListResponse> {
    return {
      totalItems: this.mockChats.length,
      totalPages: 1,
      currentPage: 1,
      chats: this.mockChats,
    };
  }

  async createChat(request: CreateChatRequest): Promise<Chat> {
    const newChat: Chat = {
      chatId: Math.random().toString(36).substr(2, 9),
      title: 'New Chat',
      provider: request.provider,
      userId: 'user1',
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };
    this.mockChats.unshift(newChat);
    return newChat;
  }

  async getChatById(chatId: string): Promise<ChatWithMessagesResponse> {
    const chat = this.mockChats.find((c) => c.chatId === chatId);
    if (!chat) throw new Error('Chat not found');

    return {
      totalItems: 0,
      totalPages: 0,
      currentPage: 1,
      chat,
      messages: [],
    };
  }

  async sendMessage(chatId: string, request: SendMessageRequest): Promise<MessageResponse> {
    const userMessage = {
      messageId: Math.random().toString(36).substr(2, 9),
      chatId,
      content: request.content,
      sender: 'user' as const,
      timestamp: new Date().toISOString(),
    };

    const assistantMessage = {
      messageId: Math.random().toString(36).substr(2, 9),
      chatId,
      content: `I received your message: "${request.content}". This is a mock response.`,
      sender: 'assistant' as const,
      timestamp: new Date().toISOString(),
    };

    return {
      userMessage,
      assistantResponse: assistantMessage,
    };
  }
}

// Use mock service for development
export const mockApiService = new MockApiService();
