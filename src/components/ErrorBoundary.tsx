import React from 'react';
import { useRouteError, isRouteErrorResponse } from 'react-router';
import { Card } from './ui/card';
import { Button } from './ui/button';

export const ErrorBoundary: React.FC = () => {
  const error = useRouteError();

  let errorMessage: string;
  let errorStatus: string | number = 'Error';

  if (isRouteErrorResponse(error)) {
    errorStatus = error.status;
    errorMessage = error.statusText || 'Something went wrong';
  } else if (error instanceof Error) {
    errorMessage = error.message;
  } else {
    errorMessage = 'An unexpected error occurred';
  }

  const handleReload = () => {
    window.location.reload();
  };

  const handleGoHome = () => {
    window.location.href = '/';
  };

  return (
    <div className="flex items-center justify-center min-h-screen bg-gray-50">
      <Card className="w-full max-w-md p-8">
        <div className="text-center space-y-6">
          <div className="text-6xl font-bold text-red-500">{errorStatus}</div>
          <h1 className="text-2xl font-bold text-gray-900">Oops! Something went wrong</h1>
          <p className="text-gray-600">{errorMessage}</p>

          <div className="space-y-4">
            <Button onClick={handleReload} className="w-full">
              Reload Page
            </Button>

            <Button onClick={handleGoHome} variant="outline" className="w-full">
              Go Home
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
};
