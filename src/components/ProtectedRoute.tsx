import { useEffect } from 'react';
import { Outlet } from 'react-router';
import { useAuth } from '@/hooks/useAuth';

export const ProtectedRoute = () => {
  const { isAuthenticated, onLogin } = useAuth();

  useEffect(() => {
    const checkAuth = async () => {
      if (!isAuthenticated) {
        await onLogin();
      }
    };

    checkAuth();
  }, [isAuthenticated, onLogin]);

  
  if (isAuthenticated) {
    return <Outlet />;
  }

  return null;
};
