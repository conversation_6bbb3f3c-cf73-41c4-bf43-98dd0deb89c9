import { User, LogOut, ChevronDown, Menu } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { ModelSelector } from '@/components/ModelSelector';
import { useAuth } from '@/hooks/useAuth';
import type { Provider } from '@/types/api';

interface TopBarProps {
  username?: string;
  onMobileMenuToggle?: () => void;
  selectedModel: Provider;
  onModelChange: (model: Provider) => void;
}

export function TopBar({
  username = 'john.doe',
  onMobileMenuToggle,
  selectedModel,
  onModelChange,
}: TopBarProps) {
  const { onLogout } = useAuth();
  return (
    <div className="h-16 bg-card flex items-center justify-between px-6 border-b">
      {/* Left side - mobile menu button and model selector */}
      <div className="flex items-center gap-4">
        {/* Mobile Menu Button */}
        <Button variant="ghost" size="icon" className="md:hidden mr-2" onClick={onMobileMenuToggle}>
          <Menu className="h-5 w-5" />
        </Button>
        <ModelSelector selectedModel={selectedModel} onModelChange={onModelChange} />
      </div>

      {/* Right side - user menu */}
      <div className="flex items-center gap-4">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="flex items-center gap-2 px-3 py-2">
              <div className="w-8 h-8 bg-muted rounded-full flex items-center justify-center">
                <User className="h-4 w-4" />
              </div>
              <span className="text-sm font-medium hidden sm:block">{username}</span>
              <ChevronDown className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            <DropdownMenuItem onClick={onLogout}>
              <LogOut className="h-4 w-4 mr-2" />
              Logout
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </div>
  );
}
