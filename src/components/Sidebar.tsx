import { ChevronsLeft, Rocket, ChevronRight } from 'lucide-react';
import { useEffect, useState } from 'react';
import { cn } from '@/lib/utils';
import { mockApiService } from '@/services/api';
import type { Chat } from '@/types/api';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';

interface SidebarProps {
  isCollapsed: boolean;
  onToggleCollapse: () => void;
  onNewChat: () => void;
  onSelectChat: (chatId: string) => void;
  selectedChatId?: string;
}

export function Sidebar({
  isCollapsed,
  onToggleCollapse,
  onNewChat,
  onSelectChat,
  selectedChatId,
}: SidebarProps) {
  const [chats, setChats] = useState<Chat[]>([]);

  useEffect(() => {
    const loadChats = async () => {
      try {
        const response = await mockApiService.getUserChats();
        setChats(response.chats);
      } catch (error) {
        console.error('Failed to load chats:', error);
      }
    };
    loadChats();

    // Refresh chats periodically to show new chats
    const interval = setInterval(loadChats, 1000);
    return () => clearInterval(interval);
  }, []);

  if (isCollapsed) {
    return (
      <div className="w-16 h-screen bg-card flex flex-col border-r">
        <div className="p-4">
          <div className="w-8 h-8 bg-primary rounded flex items-center justify-center">
            <span className="text-primary-foreground font-bold text-sm">N</span>
          </div>
        </div>
        <div className="flex-1" />
        <div className="p-4">
          <Button variant="ghost" size="icon" onClick={onToggleCollapse} className="w-8 h-8">
            <ChevronsLeft className="h-4 w-4 rotate-180" />
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="w-64 md:w-64 sm:w-full h-screen bg-card flex flex-col border-r">
      {/* Header */}
      <div className="p-4">
        <div className="flex items-center gap-2 mb-4">
          <div className="w-8 h-8 bg-primary rounded flex items-center justify-center">
            <span className="text-primary-foreground font-bold text-sm">N</span>
          </div>
          <div>
            <h1 className="font-semibold text-card-foreground">Natural</h1>
            <p className="text-xs text-muted-foreground">AI Code Assistant</p>
          </div>
        </div>

        {/* New Button */}
        <Button onClick={onNewChat} className="w-full">
          <Rocket className="h-4 w-4 mr-2" />
          New
        </Button>
      </div>

      {/* Chat Lists */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="space-y-4">
          {/* Pinned Chats */}
          <Collapsible>
            <CollapsibleTrigger asChild>
              <Button
                variant="ghost"
                className="flex items-center w-full justify-start p-2 text-sm font-medium text-muted-foreground hover:text-foreground"
              >
                <ChevronRight className="h-4 w-4 mr-2 transition-transform duration-200 data-[state=open]:rotate-90" />
                Pinned Chats
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="pl-6">
              <p className="text-sm text-muted-foreground py-2">No pinned chats</p>
            </CollapsibleContent>
          </Collapsible>

          {/* Recent Chats */}
          <Collapsible defaultOpen>
            <CollapsibleTrigger asChild>
              <Button
                variant="ghost"
                className="flex items-center w-full justify-start p-2 text-sm font-medium text-muted-foreground hover:text-foreground"
              >
                <ChevronRight className="h-4 w-4 mr-2 transition-transform duration-200 data-[state=open]:rotate-90" />
                Recent
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="pl-6 space-y-1">
              {chats.length === 0 ? (
                <p className="text-sm text-muted-foreground py-2">No recent chats</p>
              ) : (
                chats.map((chat) => (
                  <button
                    key={chat.chatId}
                    onClick={() => onSelectChat(chat.chatId)}
                    className={cn(
                      'w-full text-left p-2 rounded-md text-sm hover:bg-accent hover:text-accent-foreground',
                      selectedChatId === chat.chatId
                        ? 'bg-accent text-accent-foreground font-medium'
                        : '',
                    )}
                  >
                    <span className="truncate">{chat.title}</span>
                  </button>
                ))
              )}
            </CollapsibleContent>
          </Collapsible>
        </div>
      </div>

      {/* Collapse Button */}
      <div className="p-4 flex justify-end">
        <Button variant="ghost" size="icon" onClick={onToggleCollapse} className="w-8 h-8">
          <ChevronsLeft className="h-4 w-4" />
        </Button>
      </div>
    </div>
  );
}
