import { ChatWindow } from '@/components/ChatWindow';
import { Sidebar } from '@/components/Sidebar';
import { TopBar } from '@/components/TopBar';
import { mockApiService } from '@/services/api';
import type { Chat, Message, Provider } from '@/types/api';
import { useState } from 'react';

export function ChatInterface() {
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);
  const [isMobileSidebarOpen, setIsMobileSidebarOpen] = useState(false);
  const [selectedChatId, setSelectedChatId] = useState<string | undefined>();
  const [messages, setMessages] = useState<Message[]>([]);
  const [currentChat, setCurrentChat] = useState<Chat | undefined>();
  const [selectedModel, setSelectedModel] = useState<Provider>('azure_openai');

  const handleToggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  const handleNewChat = () => {
    // Just reset to home screen without creating a chat
    setSelectedChatId(undefined);
    setCurrentChat(undefined);
    setMessages([]);
  };

  const handleSelectChat = async (chatId: string) => {
    try {
      setSelectedChatId(chatId);
      const response = await mockApiService.getChatById(chatId);
      setCurrentChat(response.chat);
      setMessages(response.messages);
    } catch (error) {
      console.error('Failed to load chat:', error);
    }
  };

  const handleSendMessage = async (content: string) => {
    let chatId = selectedChatId;
    let isNewChat = false;

    // Create new chat if none selected
    if (!chatId) {
      try {
        const newChat = await mockApiService.createChat({ provider: selectedModel });
        chatId = newChat.chatId;
        setSelectedChatId(chatId);
        setCurrentChat(newChat);
        setMessages([]);
        isNewChat = true;
      } catch (error) {
        console.error('Failed to create new chat:', error);
        return;
      }
    }

    try {
      const response = await mockApiService.sendMessage(chatId, {
        content,
        provider: currentChat?.provider || selectedModel,
      });

      setMessages((prev) => [...prev, response.userMessage, response.assistantResponse]);

      // Update chat title if it's the first message
      if (messages.length === 0 && currentChat) {
        const updatedChat = {
          ...currentChat,
          title: content.length > 50 ? content.substring(0, 50) + '...' : content,
        };
        setCurrentChat(updatedChat);

        // Force refresh the sidebar to show the new chat in Recents
        // This simulates the chat appearing after the AI response
        if (isNewChat) {
          // Trigger a refresh of the sidebar chat list
          setTimeout(() => {
            // The sidebar will automatically refresh due to its polling mechanism
          }, 100);
        }
      }
    } catch (error) {
      console.error('Failed to send message:', error);
    }
  };

  return (
    <div className="flex h-screen bg-background">
      {/* Desktop Sidebar */}
      <div className="hidden md:block">
        <Sidebar
          isCollapsed={isSidebarCollapsed}
          onToggleCollapse={handleToggleSidebar}
          onNewChat={handleNewChat}
          onSelectChat={handleSelectChat}
          selectedChatId={selectedChatId}
        />
      </div>

      {/* Mobile Sidebar Overlay */}
      {isMobileSidebarOpen && (
        <div className="fixed inset-0 z-50 md:hidden">
          <div
            className="fixed inset-0 bg-black/50"
            onClick={() => setIsMobileSidebarOpen(false)}
          />
          <div className="fixed left-0 top-0 h-full">
            <Sidebar
              isCollapsed={false}
              onToggleCollapse={() => setIsMobileSidebarOpen(false)}
              onNewChat={() => {
                handleNewChat();
                setIsMobileSidebarOpen(false);
              }}
              onSelectChat={(chatId) => {
                handleSelectChat(chatId);
                setIsMobileSidebarOpen(false);
              }}
              selectedChatId={selectedChatId}
            />
          </div>
        </div>
      )}

      <div className="flex-1 flex flex-col">
        <TopBar
          onMobileMenuToggle={() => setIsMobileSidebarOpen(true)}
          selectedModel={selectedModel}
          onModelChange={setSelectedModel}
        />
        <ChatWindow messages={messages} onSendMessage={handleSendMessage} />
      </div>
    </div>
  );
}
