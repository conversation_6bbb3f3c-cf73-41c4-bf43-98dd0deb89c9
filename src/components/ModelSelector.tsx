import { ChevronDown } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import type { Provider } from '@/types/api';

const MODEL_OPTIONS: { value: Provider; label: string }[] = [
  { value: 'openai', label: 'OpenAI GPT' },
  { value: 'azure_openai', label: 'Azure OpenAI' },
  { value: 'google_gemini', label: 'Google Gemini' },
];

interface ModelSelectorProps {
  selectedModel: Provider;
  onModelChange: (model: Provider) => void;
}

export function ModelSelector({ selectedModel, onModelChange }: ModelSelectorProps) {
  const selectedOption = MODEL_OPTIONS.find((option) => option.value === selectedModel);

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" className="flex items-center gap-2 px-3 py-2">
          <span className="text-sm font-medium">{selectedOption?.label}</span>
          <ChevronDown className="h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="start" className="w-48">
        {MODEL_OPTIONS.map((option) => (
          <DropdownMenuItem
            key={option.value}
            onClick={() => onModelChange(option.value)}
            className={selectedModel === option.value ? 'font-medium' : ''}
          >
            {option.label}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
