import React, { useState } from 'react';
import { Send, ArrowRight } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardTitle } from '@/components/ui/card';
import type { Message } from '@/types/api';

interface ChatWindowProps {
  chatId?: string;
  messages: Message[];
  onSendMessage: (content: string) => void;
}

const suggestionCards = [
  {
    title: 'Explore Documentation',
    description: 'Get detailed, AI-driven answers to improve understanding of documentation.',
    icon: '📚',
  },
  {
    title: 'Generate Code',
    description:
      'Create custom code effortlessly by letting Natural AI generate optimized, ready-to-use solutions.',
    icon: '⚡',
  },
  {
    title: 'Explain Code',
    description:
      'Want an instant summary of a natural code? Let Natural AI inspect it via file browsing or code pasting!',
    icon: '🔍',
  },
];

export function ChatWindow({ messages, onSendMessage }: Omit<ChatWindowProps, 'chatId'>) {
  const [inputValue, setInputValue] = useState('');

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (inputValue.trim()) {
      onSendMessage(inputValue.trim());
      setInputValue('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  return (
    <div className="flex-1 flex flex-col h-screen bg-background">
      {/* Main Content Area */}
      <div className="flex-1 flex flex-col justify-center items-center px-4 md:px-8 py-8 md:py-12">
        {messages.length === 0 ? (
          <>
            {/* Welcome Section */}
            <div className="text-center mb-12">
              <h1 className="text-xl md:text-2xl font-semibold text-foreground mb-2 text-center px-4">
                What's your next feature? Lets make it happen together!
              </h1>
            </div>

            {/* Suggestion Cards */}
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 md:gap-6 max-w-5xl w-full mb-8 md:mb-12 px-4 md:px-0">
              {suggestionCards.map((card, index) => (
                <Card
                  key={index}
                  className="cursor-pointer hover:shadow-md transition-shadow min-h-[120px]"
                  onClick={() => onSendMessage(card.title)}
                >
                  <CardContent className="px-4 py-1 h-full">
                    <div className="flex gap-4 h-full">
                      <div className="text-xl flex items-center h-full">{card.icon}</div>
                      <div className="flex-1 flex flex-col justify-start">
                        <CardTitle className="text-base font-medium mb-1">{card.title}</CardTitle>
                        <CardDescription className="text-sm leading-relaxed">
                          {card.description}
                        </CardDescription>
                      </div>
                      <div className="flex items-center h-full">
                        <ArrowRight className="w-5 h-5 text-primary flex-shrink-0" />
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </>
        ) : (
          <div className="flex-1 w-full max-w-4xl">
            {/* Messages */}
            <div className="space-y-4 mb-4">
              {messages.map((message) => (
                <div
                  key={message.messageId}
                  className={`flex ${message.sender === 'user' ? 'justify-end' : 'justify-start'}`}
                >
                  <div
                    className={`max-w-xs lg:max-w-md px-4 py-3 rounded-lg ${
                      message.sender === 'user'
                        ? 'ml-auto bg-primary text-primary-foreground'
                        : 'bg-card border text-card-foreground shadow-sm'
                    }`}
                  >
                    <p className="text-sm">{message.content}</p>
                    <p className="text-xs mt-1 opacity-70">
                      {new Date(message.timestamp).toLocaleTimeString()}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* Input Area */}
      <div className="bg-card p-4 border-t">
        <div className="max-w-4xl mx-auto px-2 md:px-0">
          <form onSubmit={handleSubmit} className="flex gap-2">
            <div className="flex-1 relative">
              <Input
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                onKeyDown={handleKeyDown}
                placeholder="Type something"
                className="pr-12"
              />
              <Button
                type="submit"
                size="icon"
                className="absolute right-2 top-1/2 transform -translate-y-1/2 h-8 w-8"
                disabled={!inputValue.trim()}
              >
                <Send className="h-4 w-4" />
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
