import { Route, Routes } from 'react-router';
import { ProtectedRoute } from './components/ProtectedRoute';
import { ErrorBoundary } from './components/ErrorBoundary';
import { ChatInterface } from './components/ChatInterface';

function App() {
  return (
    <Routes>
      <Route element={<ProtectedRoute />} errorElement={<ErrorBoundary />}>
        <Route index element={<ChatInterface />} />
        <Route path="*" element={<ChatInterface />} />
      </Route>
    </Routes>
  );
}

export default App;
