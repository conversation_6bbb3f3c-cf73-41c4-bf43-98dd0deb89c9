import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import { BrowserRouter } from 'react-router';
import './index.css';
import { AuthProvider } from './contexts/AuthContext';
import { authService, authConfig } from './utils/virava';
import { client } from './lib/api/client.gen';
import App from './App';

const getConfig = async () => {
  await authService.init(authConfig);

  // Set base URL for generated API client
  client.setConfig({
    baseUrl: import.meta.env.VITE_API_BASE_URL || 'http://localhost:3000',
  });
};

getConfig()
  .then(() => {
    createRoot(document.getElementById('root')!).render(
      <StrictMode>
        <BrowserRouter>
          <AuthProvider>
            <App />
          </AuthProvider>
        </BrowserRouter>
      </StrictMode>,
    );
  })
  .catch((err) => {
    console.error('Failed to initialize auth service', err);
  });
