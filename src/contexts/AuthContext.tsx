import { authService, extractAccessToken, extractUsername } from '@/utils/virava';
import { useCallback, useMemo, useState, type FC, type ReactNode } from 'react';
import { AuthContext } from './AuthContextDefinition';

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: FC<AuthProviderProps> = ({ children }) => {
  // Check initial auth state synchronously, just like uwekind
  const initialAuthState = authService.isAuthenticated();
  
  const [isAuthenticated, setIsAuthenticated] = useState(initialAuthState);
  const [username, setUsername] = useState<string | null>(() => {
    if (initialAuthState) {
      return extractUsername();
    }
    return null;
  });

  const onLogin = useCallback(async () => {
    await authService.login();
    setIsAuthenticated(true);
    setUsername(extractUsername());
  }, []);

  const onLogout = useCallback(async () => {
    await authService.logout(window.location.origin);
    setIsAuthenticated(false);
    setUsername(null);
  }, []);

  const getAccessToken = () => {
    return extractAccessToken();
  };

  const contextValue = useMemo(
    () => ({
      isAuthenticated,
      onLogin,
      onLogout,
      getAccessToken,
      username,
      setUsername,
    }),
    [isAuthenticated, username],
  );

  return <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>;
};
