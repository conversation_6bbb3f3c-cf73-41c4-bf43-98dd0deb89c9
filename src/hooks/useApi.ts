import { useCallback } from 'react';
import { useNavigate } from 'react-router';
import { useAuth } from './useAuth';
import { interceptRequest as baseInterceptor } from '@/utils/interceptor';

export function useApi() {
  const { getAccessToken, onLogout } = useAuth();
  const navigate = useNavigate();

  const interceptRequest = useCallback(
    async <Type>(
      serviceFunc: (...args: unknown[]) => Promise<Type>,
      options: { headers?: Record<string, string> } = {},
      ...args: unknown[]
    ): Promise<Type> => {
      return await baseInterceptor(
        serviceFunc,
        {
          ...options,
          getAccessToken,
          onLogout,
          navigate,
        },
        ...args,
      );
    },
    [getAccessToken, onLogout, navigate],
  );

  return { interceptRequest };
}
