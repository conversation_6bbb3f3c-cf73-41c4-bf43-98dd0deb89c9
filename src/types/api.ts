export type Provider = 'openai' | 'azure_openai' | 'google_gemini';

export interface UserProfile {
  userId: string;
  username: string;
  role: 'basic_user' | 'admin' | 'moderator';
  createdAt: string;
}

export interface Chat {
  chatId: string;
  title: string;
  provider: Provider;
  userId: string;
  createdAt: string;
  updatedAt: string;
}

export interface Message {
  messageId: string;
  chatId: string;
  content: string;
  sender: 'user' | 'assistant';
  timestamp: string;
}

export interface ChatListResponse {
  totalItems: number;
  totalPages: number;
  currentPage: number;
  chats: Chat[];
}

export interface ChatWithMessagesResponse {
  totalItems: number;
  totalPages: number;
  currentPage: number;
  chat: Chat;
  messages: Message[];
}

export interface CreateChatRequest {
  provider: Provider;
}

export interface SendMessageRequest {
  content: string;
  provider: Provider;
}

export interface MessageResponse {
  userMessage: Message;
  assistantResponse: Message;
}

export interface ErrorResponse {
  error: {
    type: string;
    title: string;
    status: number;
    detail?: string;
  };
}
