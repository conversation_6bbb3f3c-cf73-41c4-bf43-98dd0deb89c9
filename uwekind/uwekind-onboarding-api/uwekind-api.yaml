openapi: 3.0.0
info:
  title: uwekind-service
  version: 1.0.0
servers:
  - url: https://uwekind-service-onboarding.thepineslab.net/api
components:
  schemas:
    ErrorResponse:
      type: object
      properties:
        error:
          type: string
          example: Invalid applicant ID.
    ApplicantRequest:
      type: object
      required:
        - application_type
        - name
        - middle_name
        - last_name
        - date_of_birth
        - profile_picture
        - family_language
        - english_proficiency
        - german_proficiency
        - parent_name
        - parent_email
        - parent_phone
        - motivation_for_applying
        - parent_expectation
        - learned_about_uvekind
      properties:
        application_type:
          type: string
          $ref: '#/components/schemas/ApplicationTypeEnum'
        name:
          type: string
          maxLength: 255
        middle_name:
          type: string
          maxLength: 255
        last_name:
          type: string
          maxLength: 255
        date_of_birth:
          type: string
          format: date
        profile_picture:
          type: string
          format: binary
        citizenship:
          type: string
          maxLength: 255
        applied_for_grade:
          type: string
          $ref: '#/components/schemas/GradeLevelEnum'
        academic_year:
          type: string
          pattern: ^\d{4}-\d{4}$
        previous_kindergarten_type:
          type: string
          maxLength: 255
        family_language:
          type: string
          maxLength: 255
        english_proficiency:
          type: string
          $ref: '#/components/schemas/CefrLevelEnum'
        german_proficiency:
          type: string
          $ref: '#/components/schemas/CefrLevelEnum'
        recommendation:
          type: string
          format: binary
        current_school:
          type: string
          maxLength: 255
        previous_school:
          type: string
          maxLength: 255
        ib_program_participation:
          type: string
          $ref: '#/components/schemas/IbProgramEnum'
        studied_languages:
          type: string
          maxLength: 512
        previous_grades_reference:
          type: string
          format: binary
        current_grades_reference:
          type: string
          format: binary
        parent_name:
          type: string
          maxLength: 255
        parent_email:
          type: string
          format: email
          maxLength: 255
        parent_phone:
          type: string
          maxLength: 20
        child_health_data:
          type: string
          maxLength: 2048
        motivation_for_applying:
          type: string
          maxLength: 2048
        parent_expectation:
          type: string
          maxLength: 2048
        parent_school_activity:
          type: string
          maxLength: 255
        learned_about_uvekind:
          type: string
          maxLength: 255
    Applicant:
      allOf:
        - $ref: '#/components/schemas/ApplicantRequest'
        - type: object
          properties:
            created_at:
              type: string
              format: date-time
            updated_at:
              type: string
              format: date-time
            applicant_id:
              type: integer
    ApplicationTypeEnum:
      type: string
      enum:
        - Детска градина
        - ПК-4 клас
        - 5-12 клас
      x-enumNames:
        - KINDERKARTEN
        - BEGGINER_CLASSES
        - ADVANCED_CLASSES
    CefrLevelEnum:
      type: string
      enum:
        - изучавало
        - не изучавало
        - майчин език
      x-enumNames:
        - LEARNED
        - NOT_LEARNED
        - MOTHER_TOUNGE
    IbProgramEnum:
      type: string
      enum:
        - PYP
        - MYP
        - DP
    GradeLevelEnum:
      type: string
      enum:
        - Детска градина 2
        - Детска градина 3
        - Подготвителен клас
        - Клас 1
        - Клас 2
        - Клас 3
        - Клас 4
        - Клас 5
        - Клас 6
        - Клас 7
        - Клас 8
        - Клас 9
        - Клас 10
        - Клас 11
        - Клас 12
      x-enumNames:
        - Kinder_2
        - Kinder_3
        - PREPARATORY
        - CLASS_1
        - CLASS_2
        - CLASS_3
        - CLASS_4
        - CLASS_5
        - CLASS_6
        - CLASS_7
        - CLASS_8
        - CLASS_9
        - CLASS_10
        - CLASS_11
        - CLASS_12
    DeleteApplicantResponse:
      type: object
      properties:
        message:
          type: string
          example: Applicant deleted successfully.
        deletedApplicant:
          $ref: '#/components/schemas/Applicant'
    DeleteMultipleApplicantsRequest:
      type: object
      required:
        - applicant_ids
      properties:
        applicant_ids:
          type: array
          items:
            type: integer
    ApplicantUpdateRequest:
      type: object
      required:
        - application_type
        - name
        - middle_name
        - last_name
        - date_of_birth
        - profile_picture
        - family_language
        - english_proficiency
        - german_proficiency
        - parent_name
        - parent_email
        - parent_phone
        - child_health_data
        - motivation_for_applying
        - parent_expectation
      properties:
        application_type:
          type: string
          $ref: '#/components/schemas/ApplicationTypeEnum'
        name:
          type: string
          maxLength: 255
        middle_name:
          type: string
          maxLength: 255
        last_name:
          type: string
          maxLength: 255
        date_of_birth:
          type: string
          format: date
        profile_picture:
          type: string
          maxLength: 512
        citizenship:
          type: string
          maxLength: 255
        applied_for_grade:
          type: string
          $ref: '#/components/schemas/GradeLevelEnum'
        academic_year:
          type: string
          maxLength: 9
        previous_kindergarten_type:
          type: string
          maxLength: 255
        family_language:
          type: string
          maxLength: 255
        english_proficiency:
          type: string
          $ref: '#/components/schemas/CefrLevelEnum'
        german_proficiency:
          type: string
          $ref: '#/components/schemas/CefrLevelEnum'
        recommendation:
          type: string
          maxLength: 512
        current_school:
          type: string
          maxLength: 255
        previous_school:
          type: string
          maxLength: 255
        ib_program_participation:
          type: string
          $ref: '#/components/schemas/IbProgramEnum'
        studied_languages:
          type: string
          maxLength: 512
        previous_grades_reference:
          type: string
          maxLength: 512
        current_grades_reference:
          type: string
          maxLength: 512
        parent_name:
          type: string
          maxLength: 255
        parent_email:
          type: string
          format: email
          maxLength: 255
        parent_phone:
          type: string
          maxLength: 20
        child_health_data:
          type: string
          maxLength: 2048
        motivation_for_applying:
          type: string
          maxLength: 2048
        parent_expectation:
          type: string
          maxLength: 2048
        parent_school_activity:
          type: string
          maxLength: 255
        learned_about_uvekind:
          type: string
          maxLength: 255
    PaginatedApplicantsResponse:
      type: object
      properties:
        total_count:
          type: integer
          description: The total number of applicants found.
        applicants:
          type: array
          items:
            $ref: '#/components/schemas/Applicant'
    AdmissionsListResponse:
      type: object
      properties:
        admissions:
          type: array
          items:
            $ref: '#/components/schemas/Admission'
        totalCount:
          type: integer
          description: Total number of admissions available
    Admission:
      type: object
      properties:
        admission_id:
          type: integer
        primary_teacher:
          type: string
        primary_psychologist:
          type: string
        admission_date:
          type: string
          format: date-time
        admission_type:
          $ref: '#/components/schemas/AdmissionTypeEnum'
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
    AdmissionTypeEnum:
      type: string
      enum:
        - Детска градина
        - ПК-4 клас
        - 5-12 клас
      x-enumNames:
        - KINDERKARTEN
        - BEGGINER_CLASSES
        - ADVANCED_CLASSES
    AdmissionRequest:
      type: object
      required:
        - primary_teacher
        - primary_psychologist
        - admission_date
        - admission_type
      properties:
        primary_teacher:
          type: string
        primary_psychologist:
          type: string
        admission_date:
          type: string
          format: date-time
        admission_type:
          $ref: '#/components/schemas/AdmissionTypeEnum'
    AdmissionUpdateRequest:
      type: object
      required:
        - primary_teacher
        - primary_psychologist
        - admission_date
      properties:
        primary_teacher:
          type: string
        primary_psychologist:
          type: string
        admission_date:
          type: string
          format: date-time
    AssignStudentsRequest:
      type: object
      required:
        - student_ids
      properties:
        student_ids:
          type: array
          items:
            type: integer
            description: The IDs of the students to be assigned
    AdmissionMaterialsRequest:
      type: object
      properties:
        admission_id:
          type: integer
          description: The ID of the admission to which the materials are related.
        materials:
          type: array
          items:
            type: string
            format: binary
          description: An array of files to be uploaded.
      required:
        - admission_id
        - materials
    AdmissionMaterials:
      type: object
      properties:
        material_id:
          type: integer
        file_name:
          type: string
          maxLength: 255
        admission_id:
          type: integer
        bucket_file_name:
          type: string
          maxLength: 255
          description: The pre-signed URL for accessing the file in the bucket.
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      required:
        - material_id
        - file_name
        - admission_id
        - bucket_file_name
        - created_at
        - updated_at
    ApplicantConversionRequest:
      type: object
      properties:
        applicantId:
          type: integer
          description: Unique identifier of the applicant to convert
        status:
          $ref: '#/components/schemas/StatusEnum'
      required:
        - applicantId
        - status
    ApplicantConversionResponse:
      type: object
      properties:
        message:
          type: string
          example: Applicant converted to student and deleted.
        student:
          $ref: '#/components/schemas/Student'
        deletedApplicant:
          $ref: '#/components/schemas/Applicant'
    Student:
      type: object
      properties:
        student_id:
          type: integer
        application_type:
          $ref: '#/components/schemas/ApplicationTypeEnum'
        name:
          type: string
          maxLength: 255
        middle_name:
          type: string
          maxLength: 255
        last_name:
          type: string
          maxLength: 255
        date_of_birth:
          type: string
          format: date
        profile_picture:
          type: string
          maxLength: 512
        citizenship:
          type: string
          maxLength: 255
        applied_for_grade:
          $ref: '#/components/schemas/GradeLevelEnum'
        academic_year:
          type: string
          maxLength: 9
        previous_kindergarten_type:
          type: string
          maxLength: 255
        family_language:
          type: string
          maxLength: 255
        english_proficiency:
          $ref: '#/components/schemas/CefrLevelEnum'
        german_proficiency:
          $ref: '#/components/schemas/CefrLevelEnum'
        recommendation:
          type: string
          maxLength: 512
        current_school:
          type: string
          maxLength: 255
        previous_school:
          type: string
          maxLength: 255
        ib_program_participation:
          $ref: '#/components/schemas/IbProgramEnum'
        studied_languages:
          type: string
          maxLength: 512
        previous_grades_reference:
          type: string
          maxLength: 512
        current_grades_reference:
          type: string
          maxLength: 512
        parent_name:
          type: string
          maxLength: 255
        parent_email:
          type: string
          format: email
          maxLength: 255
        parent_phone:
          type: string
          maxLength: 20
        child_health_data:
          type: string
          maxLength: 2048
        motivation_for_applying:
          type: string
          maxLength: 2048
        parent_expectation:
          type: string
          maxLength: 2048
        parent_school_activity:
          type: string
          maxLength: 255
        learned_about_uvekind:
          type: string
          maxLength: 255
        status:
          $ref: '#/components/schemas/StatusEnum'
        psychologist_date:
          type: string
          format: date-time
        admission_id:
          type: integer
        full_name:
          type: string
          maxLength: 255
        identity_number:
          type: string
          maxLength: 10
        mother_name:
          type: string
          maxLength: 255
        mother_email:
          type: string
          format: email
          maxLength: 255
        mother_phone:
          type: string
          maxLength: 20
        father_name:
          type: string
          maxLength: 255
        father_email:
          type: string
          format: email
          maxLength: 255
        father_phone:
          type: string
          maxLength: 20
        is_tax_payed:
          type: boolean
        payment_id:
          type: integer
        grade_level_class:
          $ref: '#/components/schemas/GradeLevelClassEnum'
        applied_for_academic_year:
          type: string
          maxLength: 9
        current_grade:
          type: string
          $ref: '#/components/schemas/GradeLevelEnum'
        student_type:
          type: string
          $ref: '#/components/schemas/StudentTypeEnum'
        previous_status:
          typr: string
          $ref: '#/components/schemas/StatusEnum'
        is_student:
          type: boolean
        in_process:
          type: boolean
        public_link:
          type: string
          maxLength: 255
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
        left_school_at:
          type: string
          format: date-time
        has_teacher_comment:
          type: boolean
        insurance_month:
          type: integer
      required:
        - student_id
        - application_type
        - name
        - middle_name
        - last_name
        - date_of_birth
        - profile_picture
        - family_language
        - english_proficiency
        - german_proficiency
        - motivation_for_applying
        - parent_expectation
        - learned_about_uvekind
        - status
    StatusEnum:
      type: string
      enum:
        - Приета кандидатура
        - Поканен
        - Отказан
        - Непотвърдил
        - Приемна сесия
        - Одобрен
        - Чакащ
        - Потвърдил прием
        - Попълнена адресна карта
        - В процес на подписване
        - Подписани документи от родител
        - Готова документация
        - Готов за ученик
        - Завършил
        - Напуснал
      x-enumNames:
        - APPLICATION_ACCEPTED
        - INVITED
        - REJECTED
        - UNCONFIRMED
        - ADMISSION
        - APPROVED
        - WAITING
        - CONFIMED_ADDMISION
        - ADDRESS_CARD_COMPLETED
        - IN_SIGNING_PROCESS
        - DOCUMENTS_SIGNED_BY_PARENT
        - READY_DOCUMENTATION
        - STUDENT_READY
        - GRADUATED
        - DROPPED_OUT
    GradeLevelClassEnum:
      type: string
      enum:
        - А
        - Б
        - В
      x-enumNames:
        - A
        - B
        - C
    StudentTypeEnum:
      type: string
      enum:
        - Ученик
        - Служебен
        - Стипендиант
      x-enumNames:
        - STUDENT
        - OFFICIAL
        - SCHOLARSHIP
    ApplicantsStatusRequest:
      type: object
      properties:
        applicant_ids:
          type: array
          items:
            type: integer
          description: Array of applicant IDs to be converted to students
        status:
          $ref: '#/components/schemas/StatusEnum'
      required:
        - applicant_ids
        - status
    StudentResponse:
      type: object
      properties:
        students:
          type: array
          items:
            $ref: '#/components/schemas/Student'
        totalCount:
          type: integer
          description: Total count of students matching the filters
    StudentTaxStatusRequest:
      type: object
      properties:
        payment_id:
          type: integer
    StudentStatusUpdateResponse:
      type: object
      properties:
        student_id:
          type: integer
        status:
          $ref: '#/components/schemas/StatusEnum'
    StudentStatusUpdateRequest:
      type: object
      required:
        - status
      properties:
        status:
          $ref: '#/components/schemas/StatusEnum'
    FeedbackRequest:
      type: object
      required:
        - internal_feedback
        - external_feedback
        - teacher_name
        - subject
        - weight
        - feedback_type
        - student_id
      properties:
        internal_feedback:
          type: string
        external_feedback:
          type: string
        teacher_name:
          type: string
        subject:
          type: string
        weight:
          type: integer
        feedback_type:
          $ref: '#/components/schemas/FeedbackTypeEnum'
        student_id:
          type: integer
    FeedbackTypeEnum:
      type: string
      enum:
        - SUBJECT
        - CONCLUSION
    Feedback:
      allOf:
        - $ref: '#/components/schemas/FeedbackRequest'
        - type: object
          required:
            - feedback_id
          properties:
            feedback_id:
              type: integer
    FeedbackUpdateRequest:
      type: object
      required:
        - internal_feedback
        - external_feedback
        - teacher_name
        - subject
        - weight
      properties:
        internal_feedback:
          type: string
          maxLength: 8192
        external_feedback:
          type: string
          maxLength: 8192
        teacher_name:
          type: string
          maxLength: 255
        subject:
          type: string
          maxLength: 255
        weight:
          type: integer
    FeedbackWeightUpdateRequest:
      type: object
      required:
        - feedback_id
        - weight
      properties:
        feedback_id:
          type: integer
        weight:
          type: integer
    SubjectRequest:
      type: object
      required:
        - name
        - type
      properties:
        name:
          type: string
          maxLength: 255
        type:
          $ref: '#/components/schemas/AdmissionTypeEnum'
    Subject:
      type: object
      properties:
        subject_id:
          type: integer
        name:
          type: string
          maxLength: 255
        type:
          $ref: '#/components/schemas/AdmissionTypeEnum'
    UpdateStudentStatusRequest:
      type: object
      properties:
        status:
          $ref: '#/components/schemas/StatusEnum'
      required:
        - status
    AddressCard:
      type: object
      properties:
        address_card_id:
          type: integer
        academic_year:
          type: string
          maxLength: 9
        name:
          type: string
          maxLength: 255
        middle_name:
          type: string
          maxLength: 255
        last_name:
          type: string
          maxLength: 255
        citizenship:
          type: string
          maxLength: 255
        date_of_birth:
          type: string
          maxLength: 255
        identity_number:
          type: string
          maxLength: 10
        birth_place:
          type: string
          maxLength: 255
        current_address:
          type: string
          maxLength: 255
        doctor:
          type: string
          maxLength: 255
        doctor_phone:
          type: string
          maxLength: 255
        child_health_data:
          type: string
          maxLength: 2048
        deceased_mother:
          type: boolean
        mother_name:
          type: string
          maxLength: 255
        mother_middle_name:
          type: string
          maxLength: 255
        mother_last_name:
          type: string
          maxLength: 255
        mother_identity_number:
          type: string
          maxLength: 10
        mother_phone:
          type: string
          maxLength: 20
        mother_work_phone:
          type: string
          maxLength: 20
        mother_email:
          type: string
          maxLength: 255
        mother_alternative_email:
          type: string
          maxLength: 255
        mother_work:
          type: string
          maxLength: 255
        deceased_father:
          type: boolean
        father_name:
          type: string
          maxLength: 255
        father_middle_name:
          type: string
          maxLength: 255
        father_last_name:
          type: string
          maxLength: 255
        father_identity_number:
          type: string
          maxLength: 10
        father_phone:
          type: string
          maxLength: 20
        father_work_phone:
          type: string
          maxLength: 20
        father_email:
          type: string
          maxLength: 255
        father_alternative_email:
          type: string
          maxLength: 255
        father_work:
          type: string
          maxLength: 255
        picture_agreement:
          type: boolean
          nullable: true
        entertainment_agreement:
          type: boolean
          nullable: true
        leave_alone_agreement:
          type: boolean
          nullable: true
        companions:
          type: string
          maxLength: 512
          nullable: true
        installments_count:
          type: string
          maxLength: 255
        student_id:
          type: integer
        is_kindergarten:
          type: boolean
    AddressCardUpdateRequest:
      type: object
      properties:
        academic_year:
          type: string
        name:
          type: string
        middle_name:
          type: string
        last_name:
          type: string
        citizenship:
          type: string
        date_of_birth:
          type: string
          format: date
        identity_number:
          type: string
        birth_place:
          type: string
        current_address:
          type: string
        doctor:
          type: string
        doctor_phone:
          type: string
        child_health_data:
          type: string
        deceased_mother:
          type: boolean
        mother_name:
          type: string
          nullable: true
        mother_middle_name:
          type: string
          nullable: true
        mother_last_name:
          type: string
          nullable: true
        mother_identity_number:
          type: string
          nullable: true
        mother_phone:
          type: string
          nullable: true
        mother_work_phone:
          type: string
          nullable: true
        mother_email:
          type: string
          nullable: true
        mother_alternative_email:
          type: string
          nullable: true
        mother_work:
          type: string
          nullable: true
        deceased_father:
          type: boolean
        father_name:
          type: string
          nullable: true
        father_middle_name:
          type: string
          nullable: true
        father_last_name:
          type: string
          nullable: true
        father_identity_number:
          type: string
          nullable: true
        father_phone:
          type: string
        father_work_phone:
          type: string
          nullable: true
        father_email:
          type: string
          nullable: true
        father_alternative_email:
          type: string
          nullable: true
        father_work:
          type: string
          nullable: true
        picture_agreement:
          type: boolean
        entertainment_agreement:
          type: boolean
        leave_alone_agreement:
          type: boolean
        companions:
          type: string
          nullable: true
        installments_count:
          type: string
    Document:
      type: object
      properties:
        document_id:
          type: integer
          description: Unique identifier for the document.
        academic_year:
          type: string
          description: Academic year for which the document is relevant.
          maxLength: 9
        document_name:
          type: string
          description: Name of the document.
          maxLength: 255
        bucket_file_name:
          type: string
          description: Name of the file in the storage bucket.
          maxLength: 255
        student_id:
          type: integer
          description: Foreign key to the student this document belongs to.
        doc_type:
          type: string
          $ref: '#/components/schemas/DocumentTypeEnum'
        created_at:
          type: string
          format: date-time
          description: Timestamp of when the document was created.
        updated_at:
          type: string
          format: date-time
          description: Timestamp of the last update to the document.
      required:
        - academic_year
        - document_name
        - bucket_file_name
        - doc_type
      example:
        document_id: 1
        academic_year: 2022-2023
        document_name: Admission Contract
        bucket_file_name: admission_contract_2022.pdf
        student_id: 101
        doc_type: Договор
        created_at: '2022-08-01T12:00:00Z'
        updated_at: '2022-08-01T12:00:00Z'
    DocumentTypeEnum:
      type: string
      enum:
        - Договор
        - Декларация
        - Служебна бележка
        - Документи прием и преместване
        - Издадени документи
        - Здравни документи
        - Други документи
        - Подписан договор
        - Подписана декларация
        - Подписана служебна бележка
      x-enumNames:
        - CONTRACT
        - DECLARATION
        - OFFICIAL_NOTE
        - ADMISSION_AND_TRANSFER_DOCUMENTS
        - ISSUED_DOCUMENTS
        - HEALTH_DOCUMENTS
        - OTHER_DOCUMENTS
        - SIGNED_CONTRACT
        - SIGNED_DECLARATION
        - SIGNED_OFFICIAL_NOTE
    SystemAcademicYear:
      type: object
      properties:
        system_academic_year_id:
          type: integer
          description: The unique identifier for the system academic year.
          example: 1
        current_system_academic_year:
          type: string
          description: The current academic year in the format YYYY/YYYY.
          example: 2022/2023
        previous_system_academic_year:
          type: string
          description: The previous academic year in the format YYYY/YYYY.
          example: 2021/2022
        next_system_academic_year:
          type: string
          description: The next academic year in the format YYYY/YYYY.
          example: 2023/2024
        next_next_system_academic_year:
          type: string
          description: >-
            The academic year after the next academic year in the format
            YYYY/YYYY.
          example: 2024/2025
        change_date:
          type: string
          format: date-time
          description: The timestamp when the current academic year was set.
          example: '2022-08-01T00:00:00Z'
      required:
        - system_academic_year_id
        - current_system_academic_year
        - change_date
    TeacherCommentRequest:
      type: object
      properties:
        student_id:
          type: integer
          description: The ID of the student to which the comment is linked.
        teacher_name:
          type: string
          maxLength: 255
          description: The name of the teacher adding the comment.
        comment:
          type: string
          maxLength: 2048
          description: The teacher's comment.
      required:
        - student_id
        - teacher_name
        - comment
    TeacherComment:
      type: object
      properties:
        teacher_comment_id:
          type: integer
          description: The unique identifier of the created teacher comment.
        student_id:
          type: integer
          description: The ID of the student.
        teacher_name:
          type: string
          description: The name of the teacher.
        comment:
          type: string
          description: The teacher's comment.
        created_at:
          type: string
          format: date-time
          description: The timestamp when the comment was created.
      required:
        - teacher_comment_id
        - student_id
        - teacher_name
        - comment
        - created_at
    UpdateInsuranceMonthRequest:
      type: object
      properties:
        insurance_month:
          type: integer
          description: New insurance month value to update for the student.
      required:
        - insurance_month
    ActionKeyEnum:
      type: string
      enum:
        - confirm-admission-details
        - confirm-admission
        - address-card
        - contract
    AddressCardRequest:
      type: object
      properties:
        name:
          type: string
          maxLength: 255
        middle_name:
          type: string
          maxLength: 255
        last_name:
          type: string
          maxLength: 255
        citizenship:
          type: string
          maxLength: 255
        date_of_birth:
          type: string
          format: date
        identity_number:
          type: string
          maxLength: 10
        birth_place:
          type: string
          maxLength: 255
        current_address:
          type: string
          maxLength: 255
        doctor:
          type: string
          maxLength: 255
        doctor_phone:
          type: string
          maxLength: 255
        child_health_data:
          type: string
          maxLength: 2048
        deceased_mother:
          type: boolean
        mother_name:
          type: string
          maxLength: 255
        mother_middle_name:
          type: string
          maxLength: 255
        mother_last_name:
          type: string
          maxLength: 255
        mother_identity_number:
          type: string
          maxLength: 10
        mother_phone:
          type: string
          maxLength: 20
        mother_work_phone:
          type: string
          maxLength: 20
        mother_email:
          type: string
          maxLength: 255
        mother_alternative_email:
          type: string
          maxLength: 255
        mother_work:
          type: string
          maxLength: 255
        deceased_father:
          type: boolean
        father_name:
          type: string
          maxLength: 255
        father_middle_name:
          type: string
          maxLength: 255
        father_last_name:
          type: string
          maxLength: 255
        father_identity_number:
          type: string
          maxLength: 10
        father_phone:
          type: string
          maxLength: 20
        father_work_phone:
          type: string
          maxLength: 20
        father_email:
          type: string
          maxLength: 255
        father_alternative_email:
          type: string
          maxLength: 255
        father_work:
          type: string
          maxLength: 255
        picture_agreement:
          type: boolean
        entertainment_agreement:
          type: boolean
        leave_alone_agreement:
          type: boolean
        companions:
          type: string
          maxLength: 512
          nullable: true
        installments_count:
          type: string
          maxLength: 255
    CreateStudentObligationRequest:
      type: object
      properties:
        obligation_type:
          type: string
          $ref: '#/components/schemas/ObligationTypeEnum'
        obligation_sub_type:
          type: string
        due_amount_leva:
          type: number
          format: double
        due_amount_euro:
          type: number
          format: double
        installment_count:
          type: integer
        balance_leva:
          type: number
          format: double
        balance_euro:
          type: number
          format: double
        academic_year:
          type: string
        student_id:
          type: integer
        comment:
          type: string
          nullable: true
      required:
        - obligation_type
        - due_amount_leva
        - due_amount_euro
        - balance_leva
        - balance_euro
        - academic_year
        - student_id
    StudentObligation:
      type: object
      properties:
        obligation_id:
          type: integer
        obligation_type:
          type: string
          $ref: '#/components/schemas/ObligationTypeEnum'
        obligation_sub_type:
          type: string
          nullable: true
        due_amount_leva:
          type: number
          format: double
        due_amount_euro:
          type: number
          format: double
        installment_count:
          type: integer
        balance_leva:
          type: number
          format: double
        balance_euro:
          type: number
          format: double
        academic_year:
          type: string
        comment:
          type: string
        is_deletable:
          type: boolean
        student_id:
          type: integer
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      required:
        - obligation_id
        - obligation_type
        - due_amount_leva
        - due_amount_euro
        - balance_leva
        - balance_euro
        - academic_year
        - is_deletable
        - student_id
        - created_at
        - updated_at
    ObligationTypeEnum:
      type: string
      enum:
        - Такса обучение
        - Обяд
        - Закуска
        - Транспорт 1 посока
        - Транспорт 2 посоки
        - Учебни ресурси
        - Документи, база
        - Ателиета
        - Екскурзии-Лагери
        - Други
      x-enumNames:
        - TAX
        - LUNCH
        - SNACK
        - TRANSPORT_ONE_WAY
        - TRANSPORT_TWO_WAYS
        - EDUCATIONAL_RESOURCES
        - DOCUMENTS
        - WORKSHOPS
        - TRIPS_CAMPS
        - OTHER
    ObligationsResponse:
      type: object
      properties:
        totalObligations:
          $ref: '#/components/schemas/ObligationDetails'
        previousObligations:
          $ref: '#/components/schemas/ObligationDetails'
        overpaidObligations:
          $ref: '#/components/schemas/ObligationDetails'
    ObligationDetails:
      type: object
      properties:
        total_balance_leva:
          type: string
          format: decimal
          example: '100.00'
        total_balance_evro:
          type: string
          format: decimal
          example: '51.13'
    ObligationPaymentResponse:
      type: object
      properties:
        parentPayment:
          $ref: '#/components/schemas/ObligationPayment'
        childPayments:
          type: array
          items:
            $ref: '#/components/schemas/ObligationPayment'
        updatedObligations:
          type: array
          items:
            $ref: '#/components/schemas/StudentObligation'
    CreateObligationPaymentRequest:
      type: object
      properties:
        student_id:
          type: number
        currency:
          type: string
          $ref: '#/components/schemas/CurrencyEnum'
        payment_date:
          type: string
          format: date-time
        payment_type:
          type: string
          $ref: '#/components/schemas/PaymentTypeEnum'
        payments:
          type: array
          items:
            type: object
            properties:
              amount:
                type: number
                format: double
              obligation_id:
                type: integer
      required:
        - amount
        - currency
        - payment_date
        - payment_type
        - obligation_id
    ObligationPayment:
      type: object
      properties:
        payment_id:
          type: integer
        parent_payment_id:
          type: integer
        student_id:
          type: integer
        amount:
          type: number
          format: double
        currency:
          type: string
          $ref: '#/components/schemas/CurrencyEnum'
        payment_date:
          type: string
          format: date-time
        payment_type:
          type: string
          $ref: '#/components/schemas/PaymentTypeEnum'
        obligation_id:
          type: integer
        created_at:
          type: string
          format: date-time
        updated_at:
          type: string
          format: date-time
      required:
        - payment_id
        - amount
        - currency
        - obligation_id
    CurrencyEnum:
      type: string
      enum:
        - BGN
        - EUR
    PaymentTypeEnum:
      type: string
      enum:
        - PAYMENT
        - REVERSAL
        - REFUND
    UpdateObligationPaymentRequest:
      type: object
      properties:
        student_id:
          type: number
        parent_payment_id:
          type: number
        currency:
          type: string
          $ref: '#/components/schemas/CurrencyEnum'
        payment_date:
          type: string
          format: date-time
        payment_type:
          type: string
          $ref: '#/components/schemas/PaymentTypeEnum'
        payments:
          type: array
          items:
            type: object
            properties:
              amount:
                type: number
                format: double
              obligation_id:
                type: integer
      required:
        - amount
        - currency
        - payment_date
        - payment_type
        - obligation_id
    ObligationChildPayment:
      allOf:
        - $ref: '#/components/schemas/ObligationPayment'
        - type: object
          properties:
            obligation_type:
              type: string
            obligation_sub_type:
              type: string
    ObligationSubTypesResponse:
      type: object
      properties:
        workshops:
          type: array
          items:
            type: string
        excursions:
          type: array
          items:
            type: string
        other:
          type: array
          items:
            type: string
    TableResponse:
      type: object
      properties:
        data:
          type: string
    ChildPayment:
      type: object
      properties:
        payment_id:
          type: integer
          example: 2887
        amount:
          type: number
          example: 500
        currency:
          type: string
          example: BGN
        payment_date:
          type: string
          format: date
          example: '2025-03-18'
        payment_type:
          type: string
          example: PAYMENT
        obligation:
          type: string
          example: Учебни ресурси
      required:
        - payment_id
        - amount
        - currency
        - payment_date
        - payment_type
        - obligation
    Payment:
      type: object
      properties:
        payment_id:
          type: integer
          example: 2886
        amount:
          type: number
          example: '1500.00'
        currency:
          type: string
          example: BGN
        payment_date:
          type: string
          format: date-time
          example: '2025-03-17T22:00:00.000Z'
        payment_type:
          type: string
          example: PAYMENT
        student_id:
          type: integer
          example: 1158
        full_name:
          type: string
        grade_level_class:
          type: string
        current_grade:
          type: string
        child:
          type: array
          items:
            $ref: '#/components/schemas/ChildPayment'
      required:
        - payment_id
        - amount
        - currency
        - payment_date
        - payment_type
        - student_id
        - child
    PaymentsResponse:
      type: object
      properties:
        payments:
          type: array
          items:
            $ref: '#/components/schemas/Payment'
        count:
          type: integer
          example: '1287'
      required:
        - payments
        - count
  responses:
    BadRequest:
      description: BAD REQUEST
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    Unauthorized:
      description: UNAUTHORIZED
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    Forbidden:
      description: FORBIDDEN
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    NotFound:
      description: NOT FOUND
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
    InternalServerError:
      description: INTERNAL SERVER ERROR
      content:
        application/json:
          schema:
            $ref: '#/components/schemas/ErrorResponse'
  securitySchemes:
    BEARER_JWT:
      type: http
      scheme: bearer
      bearerFormat: JWT
security:
  - BEARER_JWT: []
paths:
  /applicants:
    post:
      tags:
        - Applicants
      summary: Create a new applicant
      description: Create a new applicant with their details and upload documents.
      operationId: createApplicant
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ApplicantRequest'
      responses:
        '201':
          description: Applicant created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Applicant'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /applicants/get-years:
    get:
      tags:
        - Applicants
      summary: Retrieve the next two system academic years
      description: >-
        Fetches the next two system academic years based on the current system
        settings.
      operationId: getSystemAcademicYears
      responses:
        '200':
          description: Successfully retrieved the next two system academic years
          content:
            application/json:
              schema:
                type: object
                properties:
                  allowedYears:
                    type: array
                    items:
                      type: string
                      example:
                        - 2023-2024
                        - 2024-2025
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/applicants/{id}:
    delete:
      tags:
        - Admin
      summary: Delete an applicant by ID
      description: Deletes an applicant from the system using their unique ID.
      operationId: deleteApplicant
      parameters:
        - name: id
          in: path
          required: true
          description: Unique ID of the applicant to delete
          schema:
            type: integer
      responses:
        '200':
          description: Applicant deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DeleteApplicantResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    put:
      tags:
        - Admin
      summary: Update an applicant's information
      description: Updates the information of an applicant identified by their unique ID.
      operationId: updateApplicant
      parameters:
        - name: id
          in: path
          required: true
          description: Unique identifier of the applicant to update
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApplicantUpdateRequest'
      responses:
        '200':
          description: Applicant updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Applicant'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/applicant/{applicant_id}:
    get:
      tags:
        - Admin
      summary: Retrieve applicant information by ID
      description: >-
        Fetches detailed information of a specific applicant using their unique
        ID.
      operationId: getApplicantById
      parameters:
        - name: applicant_id
          in: path
          required: true
          description: Unique identifier of the applicant
          schema:
            type: integer
      responses:
        '200':
          description: Applicant information retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Applicant'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/applicants:
    delete:
      tags:
        - Admin
      summary: Delete multiple applicants
      description: >-
        Deletes multiple applicants from the system using a list of unique IDs
        provided in the request body.
      operationId: adminDeleteMultipleApplicants
      requestBody:
        required: true
        description: A JSON object containing an array of applicant IDs to delete
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/DeleteMultipleApplicantsRequest'
      responses:
        '200':
          description: Applicants deleted successfully.
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: 3 applicants deleted successfully.
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          description: No applicants found or deleted with the provided IDs.
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: No applicants found or deleted with the provided IDs.
        '500':
          $ref: '#/components/responses/InternalServerError'
    get:
      tags:
        - Admin
      summary: Retrieve a paginated list of applicants with optional filters
      description: >-
        Admin endpoint to fetch a list of applicants with support for filtering
        by name, date of birth range, grade level, and pagination.
      operationId: adminGetPaginatedApplicants
      parameters:
        - in: query
          name: name
          schema:
            type: string
          required: false
          description: Filter by applicant's name
        - in: query
          name: middle_name
          schema:
            type: string
          required: false
          description: Filter by applicant's middle name
        - in: query
          name: last_name
          schema:
            type: string
          required: false
          description: Filter by applicant's last name
        - in: query
          name: start_date_of_birth
          schema:
            type: string
            format: date
          required: false
          description: Filter by the start range of applicant's date of birth
        - in: query
          name: end_date_of_birth
          schema:
            type: string
            format: date
          required: false
          description: Filter by the end range of applicant's date of birth
        - in: query
          name: applied_for_grade
          schema:
            type: array
            items:
              $ref: '#/components/schemas/GradeLevelEnum'
          required: false
          description: Filter by applicant's grade level
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
          required: false
          description: Limit the number of applicants to retrieve
        - in: query
          name: offset
          schema:
            type: integer
          required: false
          description: Pagination offset
      responses:
        '200':
          description: Paginated list of applicants retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedApplicantsResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/admissions:
    get:
      tags:
        - Admin/Admissions
      summary: Retrieve a list of admissions with optional filters
      description: >-
        Admin endpoint to fetch a list of admissions with support for filtering
        by primary teacher, primary psychologist, admission date range, and
        admission type.
      operationId: adminListAdmissions
      parameters:
        - in: query
          name: limit
          schema:
            type: integer
          required: true
          description: Limit the number of admissions to retrieve
        - in: query
          name: offset
          schema:
            type: integer
          required: true
          description: Offset for pagination
        - in: query
          name: primary_teacher
          schema:
            type: string
          required: false
          description: Filter by primary teacher's name
        - in: query
          name: primary_psychologist
          schema:
            type: string
          required: false
          description: Filter by primary psychologist's name
        - in: query
          name: start_admission_date
          schema:
            type: string
            format: date
          required: false
          description: Start date for admission date range filter
        - in: query
          name: end_admission_date
          schema:
            type: string
            format: date
          required: false
          description: End date for admission date range filter
        - in: query
          name: admission_type
          schema:
            type: array
            items:
              $ref: '#/components/schemas/AdmissionTypeEnum'
          required: false
          description: Filter by admission type
      responses:
        '200':
          description: List of admissions retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AdmissionsListResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
    post:
      tags:
        - Admin/Admissions
      summary: Create a new admission record
      description: Creates a new admission record with the provided details.
      operationId: adminCreateAdmission
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdmissionRequest'
      responses:
        '201':
          description: Admission created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Admission'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/admissions/{admission_id}:
    get:
      tags:
        - Admin/Admissions
      summary: Retrieve admission details by ID
      description: >-
        Admin endpoint to fetch the details of an admission by its unique
        identifier.
      operationId: adminGetAdmissionById
      parameters:
        - name: admission_id
          in: path
          required: true
          description: Unique identifier of the admission to retrieve
          schema:
            type: integer
      responses:
        '200':
          description: Admission details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Admission'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    delete:
      tags:
        - Admin/Admissions
      summary: Delete an admission and its materials
      description: >-
        Deletes an admission record and its associated materials by the
        admission's unique identifier.
      operationId: deleteAdmissionWithMaterials
      parameters:
        - name: admission_id
          in: path
          required: true
          description: Unique identifier of the admission to delete
          schema:
            type: integer
      responses:
        '200':
          description: Admission and associated materials deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Admission and associated materials deleted successfully.
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    put:
      tags:
        - Admin/Admissions
      summary: Update an admission by ID
      description: Admin endpoint to update an admission record by its unique identifier.
      operationId: adminUpdateAdmission
      parameters:
        - name: admission_id
          in: path
          required: true
          description: Unique identifier of the admission to update
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AdmissionUpdateRequest'
      responses:
        '200':
          description: Admission updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Admission'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/admission/possible-stundents/{admission_id}:
    get:
      tags:
        - Admin/Admissions
      summary: Retrieve possible students for a given admission
      description: >-
        Fetches a list of possible students based on the admission type
        associated with the provided admission ID. Accessible by ADMIN and
        TEACHER roles.
      operationId: getAdmissionPossibleStudents
      parameters:
        - name: admission_id
          in: path
          required: true
          description: The ID of the admission to retrieve possible students for
          schema:
            type: integer
      responses:
        '200':
          description: A list of possible students retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Student'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/admission/assign-students/{admission_id}:
    patch:
      tags:
        - Admin/Admissions
      summary: Assign students to an admission
      description: >-
        Assigns a list of students to a specific admission based on the
        admission ID. Accessible by ADMIN and TEACHER roles.
      operationId: assignStudentsToAdmission
      parameters:
        - name: admission_id
          in: path
          required: true
          description: The ID of the admission to assign students to
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AssignStudentsRequest'
      responses:
        '200':
          description: Students assigned to admission successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Student'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/admission-materials:
    post:
      tags:
        - Admin/Admission-Materials
      summary: Upload admission materials
      description: >-
        Allows for the uploading of multiple admission materials associated with
        an admission ID.
      operationId: uploadAdmissionMaterials
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/AdmissionMaterialsRequest'
      responses:
        '201':
          description: Admission materials uploaded successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AdmissionMaterials'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/admission-materials/{admission_id}:
    get:
      tags:
        - Admin/Admission-Materials
      summary: Retrieve admission materials by admission ID
      description: >-
        Fetches a list of admission materials associated with a specific
        admission ID, including pre-signed URLs for file access.
      operationId: getAdmissionMaterials
      parameters:
        - name: admission_id
          in: path
          required: true
          description: The ID of the admission for which materials are being retrieved.
          schema:
            type: integer
      responses:
        '200':
          description: List of admission materials retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AdmissionMaterials'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/admission-materials/{admission_id}/{material_id}:
    delete:
      tags:
        - Admin/Admission-Materials
      summary: Delete an admission material
      description: >-
        Deletes a specific admission material and its associated file from the
        storage based on the admission ID and material ID.
      operationId: deleteAdmissionMaterial
      parameters:
        - name: admission_id
          in: path
          required: true
          description: The ID of the admission to which the material is related.
          schema:
            type: integer
        - name: material_id
          in: path
          required: true
          description: The unique identifier for the admission material to be deleted.
          schema:
            type: integer
      responses:
        '200':
          description: Admission material and associated file deleted successfully.
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: >-
                      Admission material and associated file deleted
                      successfully.
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          description: Admission material not found.
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Admission material not found.
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/applicants/convert:
    post:
      tags:
        - Admin
      summary: Convert an applicant to a student
      description: >-
        Accepts an applicant by their ID, assigns them a status, converts them
        to a student, and deletes the applicant record.
      operationId: convertApplicant
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApplicantConversionRequest'
      responses:
        '200':
          description: Applicant converted to student and deleted.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ApplicantConversionResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/students/{student_id}:
    get:
      tags:
        - Admin/Students
      summary: Retrieve a student by ID
      description: Fetches a student's data by their unique identifier.
      operationId: getStudentById
      parameters:
        - name: student_id
          in: path
          required: true
          description: Unique identifier of the student to retrieve
          schema:
            type: integer
      responses:
        '200':
          description: Student data retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Student'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/applicants/convert-multiple:
    post:
      tags:
        - Admin
      summary: Convert multiple applicants to students
      description: >-
        Accepts multiple applicants by their IDs, assigns them a status, and
        converts them to students.
      operationId: convertMultipleApplicants
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApplicantsStatusRequest'
      responses:
        '200':
          description: Applicants converted to students successfully.
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                  converted:
                    type: integer
                    description: Number of applicants converted to students.
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/students:
    get:
      tags:
        - Admin/Students
      summary: Retrieve a list of students with optional filters
      description: >-
        Fetches a list of students with support for filtering by various
        criteria and pagination.
      operationId: getStudents
      parameters:
        - name: full_name
          in: query
          required: false
          description: Filter by student's full name
          schema:
            type: string
        - name: applied_for_grade
          in: query
          required: false
          description: Filter by the grade the student has applied for
          schema:
            type: array
            items:
              $ref: '#/components/schemas/GradeLevelEnum'
        - name: current_grade
          in: query
          required: false
          description: Filter by the grade the student is currently in
          schema:
            type: array
            items:
              $ref: '#/components/schemas/GradeLevelEnum'
        - name: grade_level_class
          in: query
          required: false
          description: Filter by the class level within the grade
          schema:
            type: array
            items:
              $ref: '#/components/schemas/GradeLevelClassEnum'
        - name: identity_number
          in: query
          required: false
          description: Filter by student's identity number
          schema:
            type: string
        - name: academic_year
          in: query
          required: false
          description: Filter by the academic year
          schema:
            type: string
            pattern: ^\d{4}-\d{4}$
        - name: applied_for_academic_year
          in: query
          required: false
          description: Filter by the academic year
          schema:
            type: array
            items:
              type: string
              pattern: ^\d{4}-\d{4}$
        - name: status
          in: query
          required: false
          description: Filter by the student's application status
          schema:
            type: array
            items:
              $ref: '#/components/schemas/StatusEnum'
        - name: mother_name
          in: query
          required: false
          description: Filter by the mother's name
          schema:
            type: string
        - name: mother_email
          in: query
          required: false
          description: Filter by the mother's email
          schema:
            type: string
            format: email
        - name: mother_phone
          in: query
          required: false
          description: Filter by the mother's phone number
          schema:
            type: string
        - name: father_name
          in: query
          required: false
          description: Filter by the father's name
          schema:
            type: string
        - name: father_email
          in: query
          required: false
          description: Filter by the father's email
          schema:
            type: string
            format: email
        - name: father_phone
          in: query
          required: false
          description: Filter by the father's phone number
          schema:
            type: string
        - name: insurance_month
          in: query
          required: false
          description: Insurance month
          schema:
            type: integer
        - name: is_student
          in: query
          required: false
          description: Filter by the is_student
          schema:
            type: boolean
        - name: in_process
          in: query
          required: false
          description: Filter by the in_process
          schema:
            type: boolean
        - name: limit
          in: query
          required: false
          description: Limit the number of returned students per page (default is 10)
          schema:
            type: integer
            default: 10
        - name: offset
          in: query
          required: false
          description: Offset for pagination (default is 0)
          schema:
            type: integer
            default: 0
      responses:
        '200':
          description: List of students retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  students:
                    type: array
                    items:
                      $ref: '#/components/schemas/Student'
                  totalCount:
                    type: integer
                    description: Total count of students matching the filters
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/students/{student_id}/psychologist-date:
    patch:
      tags:
        - Admin/Students
      summary: Update student's psychologist date
      description: Updates the psychologist date for a specific student by their ID.
      operationId: updateStudentPsychologistDate
      parameters:
        - name: student_id
          in: path
          required: true
          description: Unique identifier of the student
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                psychologist_date:
                  type: string
                  format: date-time
                  description: New psychologist date for the student
      responses:
        '200':
          description: Student's psychologist date updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Student'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/students/{student_id}/admission:
    patch:
      tags:
        - Admin/Students
      summary: Update a student's admission ID
      description: Updates the admission ID for a specific student by their ID.
      operationId: updateStudentAdmission
      parameters:
        - name: student_id
          in: path
          required: true
          description: Unique identifier of the student
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                admission_id:
                  type: integer
                  nullable: true
                  description: New admission ID to be associated with the student
      responses:
        '200':
          description: Student's admission ID updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Student'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/search/identity-numbers:
    get:
      tags:
        - Admin/Students
      summary: Search for student identity numbers
      description: >-
        Allows searching for student identity numbers based on a provided search
        string.
      operationId: searchIdentityNumbers
      parameters:
        - name: searchString
          in: query
          required: true
          description: The search string to filter identity numbers
          schema:
            type: string
      responses:
        '200':
          description: A list of identity numbers matching the search string
          content:
            application/json:
              schema:
                type: object
                properties:
                  identityNumbers:
                    type: array
                    items:
                      type: string
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/students/admision/{admission_id}:
    get:
      tags:
        - Admin/Students
      summary: Retrieve a student's details by admission ID
      description: >-
        Fetches details of a student by their unique admission ID with support
        for additional filters.
      operationId: getStudentByAdmissionId
      parameters:
        - name: admission_id
          in: path
          required: true
          description: Unique identifier of the student's admission
          schema:
            type: integer
        - name: full_name
          in: query
          required: false
          description: Filter by student's full name
          schema:
            type: string
        - name: applied_for_grade
          in: query
          required: false
          description: Filter by the grade the student has applied for
          schema:
            type: array
            items:
              $ref: '#/components/schemas/GradeLevelEnum'
        - name: date_of_birth
          in: query
          required: false
          description: Filter by the student's date of birth
          schema:
            type: integer
        - name: parent_name
          in: query
          required: false
          description: Filter by parent's name
          schema:
            type: string
        - name: is_tax_payed
          in: query
          required: false
          description: Filter by tax payment status
          schema:
            type: boolean
        - name: english_proficiency
          in: query
          required: false
          description: Filter by student's English proficiency level
          schema:
            type: array
            items:
              $ref: '#/components/schemas/CefrLevelEnum'
        - name: german_proficiency
          in: query
          required: false
          description: Filter by student's German proficiency level
          schema:
            type: array
            items:
              $ref: '#/components/schemas/CefrLevelEnum'
        - name: academic_year
          in: query
          required: false
          description: Filter by the academic year
          schema:
            type: array
            items:
              type: string
              pattern: ^\d{4}-\d{4}$
        - name: status
          in: query
          required: false
          description: Filter by the student's application status
          schema:
            type: array
            items:
              $ref: '#/components/schemas/StatusEnum'
        - name: limit
          in: query
          required: false
          description: Limit the number of returned students per page (default is 10)
          schema:
            type: integer
            default: 10
        - name: offset
          in: query
          required: false
          description: Offset for pagination (default is 0)
          schema:
            type: integer
            default: 0
      responses:
        '200':
          description: Student details retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StudentResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/search/students:
    get:
      tags:
        - Admin/Students
      summary: Search for students by name or admission ID
      description: Allows searching for students based on full name or admission ID.
      operationId: searchStudents
      parameters:
        - name: full_name
          in: query
          required: true
          description: Full name of the student to search for
          schema:
            type: string
        - name: admission_id
          in: query
          required: true
          description: Admission ID of the student not to search for
          schema:
            type: integer
      responses:
        '200':
          description: A list of students matching the search criteria
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Student'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/students/invite/{studentId}:
    patch:
      tags:
        - Admin/Students
      summary: Update student status to invited and send an email
      description: >-
        Updates the status of a student to 'invited' and sends an invitation
        email if they meet certain criteria.
      operationId: inviteStudent
      parameters:
        - name: studentId
          in: path
          required: true
          description: Unique identifier of the student to invite
          schema:
            type: integer
      responses:
        '200':
          description: Status updated and email notification sent
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Status updated and email notification sent.
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/student/{student_id}/tax-status:
    post:
      tags:
        - Admin/Students
      summary: Update student's tax payment status
      description: >-
        Updates the tax payment status of a student based on the provided
        student ID.
      operationId: updateStudentTaxStatus
      parameters:
        - name: student_id
          in: path
          required: true
          description: Unique identifier of the student
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StudentTaxStatusRequest'
      responses:
        '200':
          description: Student's tax status updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Student'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/students/{studentId}:
    delete:
      tags:
        - Admin/Students
      summary: Delete a student by ID
      description: >-
        Deletes a student and their associated files by the student's unique
        identifier.
      operationId: deleteStudent
      parameters:
        - name: studentId
          in: path
          required: true
          description: Unique identifier of the student to delete
          schema:
            type: integer
      responses:
        '200':
          description: Student and associated files deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Student and associated files deleted successfully.
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/students/reject/{studentId}:
    patch:
      tags:
        - Admin/Students
      summary: Reject a student by ID
      description: >-
        Updates the status of a student to 'Отказан' (rejected) by their unique
        identifier.
      operationId: rejectStudent
      parameters:
        - name: studentId
          in: path
          required: true
          description: Unique identifier of the student to reject
          schema:
            type: integer
      responses:
        '200':
          description: Student status updated to 'Отказан'
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Student'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/student/revert/{student_id}:
    patch:
      tags:
        - Admin/Students
      summary: Revert student status
      description: >-
        Reverts the status of a student to a previous state if certain
        conditions are met.
      operationId: revertStudentStatus
      parameters:
        - name: student_id
          in: path
          required: true
          description: Unique identifier of the student whose status is to be reverted
          schema:
            type: integer
      responses:
        '200':
          description: Student status reverted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Student status reverted successfully.
                  student:
                    $ref: '#/components/schemas/StudentStatusUpdateResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/student/send-feedback/{student_id}:
    patch:
      tags:
        - Admin/Students
      summary: Send feedback to a student's parent and update student status
      description: >-
        Sends feedback for a student to their parent's email and updates the
        student's status.
      operationId: sendStudentFeedbackAndUpdateStatus
      parameters:
        - name: student_id
          in: path
          required: true
          description: Unique identifier of the student
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/StudentStatusUpdateRequest'
      responses:
        '200':
          description: Feedback sent and student status updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Student'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/students/ready-documentation/{student_id}:
    patch:
      tags:
        - Admin/Students
      summary: Update student status to 'Готова документация'
      description: >-
        Updates the status of a student to 'Готова документация' and sets the
        updated_at timestamp.
      operationId: updateStudentStatusToReadyDocumentation
      parameters:
        - name: student_id
          in: path
          required: true
          description: Unique identifier of the student whose status is to be updated
          schema:
            type: integer
      responses:
        '200':
          description: Student status updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Student'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/students/allowed-years/{student_id}:
    get:
      tags:
        - Admin/Students
      summary: Get allowed academic years for a student
      description: >-
        Fetches a list of academic years for which a student is allowed to have
        address cards.
      operationId: getStudentAllowedYears
      parameters:
        - name: student_id
          in: path
          required: true
          description: Unique identifier of the student
          schema:
            type: integer
      responses:
        '200':
          description: List of allowed academic years retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  allowedYears:
                    type: array
                    items:
                      type: string
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/students/essential/{studentId}:
    patch:
      tags:
        - Admin/Students
      summary: Update a student's information
      description: >-
        Updates a student's information including their profile picture and
        personal details.
      operationId: updateStudent
      parameters:
        - name: studentId
          in: path
          required: true
          description: The ID of the student to update
          schema:
            type: integer
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                name:
                  type: string
                middle_name:
                  type: string
                last_name:
                  type: string
                date_of_birth:
                  type: string
                  format: date
                current_grade:
                  type: string
                  $ref: '#/components/schemas/GradeLevelEnum'
                applied_for_grade:
                  type: string
                  $ref: '#/components/schemas/GradeLevelEnum'
                  nullable: true
                grade_level_class:
                  type: string
                  $ref: '#/components/schemas/GradeLevelClassEnum'
                  nullable: true
                parent_email:
                  type: string
                  format: email
                parent_phone:
                  type: string
                citizenship:
                  type: string
                  nullable: true
                identity_number:
                  type: string
                  nullable: true
                profile_picture:
                  type: string
                  format: binary
      responses:
        '200':
          description: Student updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Student'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/students/student-type/{student_id}:
    patch:
      tags:
        - Admin/Students
      summary: Update the student type of a student
      description: >-
        Updates the student type (Ученик, Служебен, Стипендиант) of a student by
        their ID.
      operationId: updateStudentType
      parameters:
        - name: student_id
          in: path
          required: true
          description: Unique identifier of the student
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                student_type:
                  type: string
                  $ref: '#/components/schemas/StudentTypeEnum'
      responses:
        '200':
          description: Student type updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Student'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/student/send-obligations/{student_id}:
    post:
      tags:
        - Admin/Students
      summary: Send obligations for a specific student
      description: >-
        Retrieves and sends the total obligations and payments for a specific
        student by their unique identifier.
      operationId: sendStudentObligations
      parameters:
        - name: student_id
          in: path
          required: true
          description: Unique identifier of the student
          schema:
            type: integer
      responses:
        '201':
          description: Obligations sent successfully
          content:
            application/json:
              schema:
                type: string
                example: Obligations sent successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/feedback:
    post:
      tags:
        - Admin/Feedback
      summary: Create new feedback
      description: Creates a new feedback entry for a student.
      operationId: createFeedback
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FeedbackRequest'
      responses:
        '201':
          description: Feedback created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Feedback'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/feedback/{feedback_id}:
    put:
      tags:
        - Admin/Feedback
      summary: Update existing feedback
      description: >-
        Updates an existing feedback entry for a student based on the provided
        feedback ID.
      operationId: updateFeedback
      parameters:
        - name: feedback_id
          in: path
          required: true
          description: Unique identifier of the feedback to update
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/FeedbackUpdateRequest'
      responses:
        '200':
          description: Feedback updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Feedback'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    delete:
      tags:
        - Admin/Feedback
      summary: Delete feedback by ID
      description: Deletes an existing feedback entry by its unique identifier.
      operationId: deleteFeedback
      parameters:
        - name: feedback_id
          in: path
          required: true
          description: Unique identifier of the feedback to delete
          schema:
            type: integer
      responses:
        '200':
          description: Feedback deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Feedback deleted successfully.
                  deletedFeedback:
                    $ref: '#/components/schemas/Feedback'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/feedback/{student_id}:
    get:
      tags:
        - Admin/Feedback
      summary: Get feedback for a specific student
      description: Retrieves all feedback entries associated with a given student ID.
      operationId: getFeedbackByStudentId
      parameters:
        - name: student_id
          in: path
          required: true
          description: Unique identifier of the student to retrieve feedback for
          schema:
            type: integer
      responses:
        '200':
          description: Feedback entries retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Feedback'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/feedback/reorder:
    put:
      tags:
        - Admin/Feedback
      summary: Reorder feedback weights
      description: Updates the weights of multiple feedback entries in a batch operation.
      operationId: reorderFeedback
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: array
              items:
                $ref: '#/components/schemas/FeedbackWeightUpdateRequest'
      responses:
        '200':
          description: Feedback weights updated successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Feedback'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/subjects:
    post:
      tags:
        - Admin/Subject
      summary: Create a new subject
      description: Creates a new subject with the provided name and type.
      operationId: createSubject
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SubjectRequest'
      responses:
        '201':
          description: Subject created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Subject'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
    get:
      tags:
        - Admin/Subject
      summary: Retrieve subjects by type
      description: Fetches a list of subjects filtered by their type.
      operationId: getSubjectsByType
      parameters:
        - name: type
          in: query
          required: true
          description: Type of the subjects to filter by
          schema:
            $ref: '#/components/schemas/AdmissionTypeEnum'
      responses:
        '200':
          description: Subjects fetched successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Subject'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/subjects/{subjectId}:
    delete:
      tags:
        - Admin/Subject
      summary: Delete a subject by ID
      description: Deletes an existing subject by its unique identifier.
      operationId: deleteSubject
      parameters:
        - name: subjectId
          in: path
          required: true
          description: Unique identifier of the subject to delete
          schema:
            type: integer
      responses:
        '200':
          description: Subject deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Subject'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/student/waiting-action/{student_id}:
    patch:
      tags:
        - Admin/Students
      summary: Update a student's status
      description: >-
        Updates the status of a student who is currently in a 'waiting' state.
        The status can be updated to either 'Отказан' (Rejected) or 'Одобрен'
        (Approved).
      operationId: updateStudentStatus
      parameters:
        - name: student_id
          in: path
          required: true
          description: The unique identifier of the student
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateStudentStatusRequest'
      responses:
        '200':
          description: Student status updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Student'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/students/siging-process/{student_id}:
    patch:
      tags:
        - Admin/Students
      summary: Update student status to 'В процес на подписване'
      description: Updates the status of a student to 'В процес на подписване'.
      operationId: updateStudentSigningStatus
      parameters:
        - name: student_id
          in: path
          required: true
          description: Unique identifier of the student whose status is to be updated
          schema:
            type: integer
      responses:
        '200':
          description: Student status updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Student'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/address-cards/{studentId}:
    get:
      tags:
        - Admin/AddressCard
      summary: Retrieve address cards by student ID
      description: Fetches address cards associated with a specific student ID.
      operationId: getAddressCardsByStudent
      parameters:
        - name: studentId
          in: path
          required: true
          description: Unique identifier of the student
          schema:
            type: integer
      responses:
        '200':
          description: Address cards retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/AddressCard'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
    post:
      tags:
        - Admin/AddressCard
      summary: Resend address cards by student ID
      description: Resend address cards associated with a specific student ID.
      operationId: resendAddressCardsByStudent
      parameters:
        - name: studentId
          in: path
          required: true
          description: Unique identifier of the student
          schema:
            type: integer
      responses:
        '204':
          description: No content
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/address-card/{id}/student/{studentId}:
    put:
      tags:
        - Admin/AddressCard
      summary: Update an address card and student information
      description: >-
        Updates the address card and student information for a given address
        card and student ID.
      operationId: updateAddressCardAndStudent
      parameters:
        - name: id
          in: path
          required: true
          description: Unique identifier of the address card
          schema:
            type: string
        - name: studentId
          in: path
          required: true
          description: Unique identifier of the student
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddressCardUpdateRequest'
      responses:
        '200':
          description: Address card and student updated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  addressCard:
                    $ref: '#/components/schemas/AddressCard'
                  student:
                    $ref: '#/components/schemas/Student'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/documents:
    post:
      tags:
        - Admin/Documents
      summary: Create a new document and upload it
      description: Create a new document with details and upload the document file.
      operationId: createDocument
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              required:
                - academic_year
                - student_id
                - doc_type
                - document_file
              properties:
                academic_year:
                  type: string
                  pattern: ^\d{4}-\d{4}$
                student_id:
                  type: integer
                doc_type:
                  type: string
                  $ref: '#/components/schemas/DocumentTypeEnum'
                document_file:
                  type: string
                  format: binary
      responses:
        '201':
          description: Document created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Document'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/students/{studentId}/documents:
    get:
      tags:
        - Admin/Students
      summary: Get all documents for a student
      description: >-
        Retrieve all documents for a student, with an option to filter by
        academic year.
      operationId: getDocumentsForStudent
      parameters:
        - name: studentId
          in: path
          required: true
          description: Unique identifier of the student
          schema:
            type: integer
        - name: academic_year
          in: query
          required: false
          description: Academic year to filter documents
          schema:
            type: string
            pattern: ^\d{4}-\d{4}$
      responses:
        '200':
          description: A list of documents
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Document'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/documents/{documentId}:
    delete:
      tags:
        - Admin/Documents
      summary: Delete a document
      description: Deletes a single document from the database and storage.
      operationId: deleteDocument
      parameters:
        - name: documentId
          in: path
          required: true
          description: The ID of the document to delete
          schema:
            type: integer
      responses:
        '200':
          description: Document deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Document deleted successfully.
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/system-academic-year/current:
    get:
      tags:
        - Admin/System
      summary: Get current system academic year
      description: Retrieves the current system academic year.
      operationId: getCurrentSystemAcademicYear
      responses:
        '200':
          description: Current system academic year retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SystemAcademicYear'
        '404':
          description: No current academic year found
        '500':
          description: Internal Server Error
  /admin/students/student-ready/{studentId}:
    patch:
      tags:
        - Admin/Students
      summary: Update student status to 'Готов за ученик'
      description: Updates the status of a student to 'Готов за ученик'.
      operationId: updateStudentStatusToStudent
      parameters:
        - name: studentId
          in: path
          required: true
          description: Unique identifier of the student whose status is to be updated
          schema:
            type: integer
      responses:
        '200':
          description: Student status updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Student'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/students/reset/{student_id}:
    patch:
      tags:
        - Admin/Students
      summary: Reset a student's status
      description: >-
        Updates the status of a student to 'Одобрен' and optionally progresses
        them to the next grade based on the provided student ID.
      operationId: updateStudentStatusToApproved
      parameters:
        - name: student_id
          in: path
          required: true
          description: The unique identifier of the student.
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - status
                - isForNextGrade
              properties:
                status:
                  $ref: '#/components/schemas/StatusEnum'
                isForNextGrade:
                  type: boolean
                  description: >-
                    Indicates whether the student should progress to the next
                    grade.
      responses:
        '200':
          description: Student status updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Student'
        '400':
          description: Invalid request parameters.
          $ref: '#/components/responses/BadRequest'
        '404':
          description: Student not found.
          $ref: '#/components/responses/NotFound'
        '500':
          description: Internal server error.
          $ref: '#/components/responses/InternalServerError'
  /admin/students/reset-multiple:
    patch:
      tags:
        - Admin/Students
      summary: Update a student's status to 'Одобрен'
      description: >-
        Updates the status of a student to 'Одобрен' based on the provided
        student ID.
      operationId: MassUpdateStudentsStatusToApproved
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                status:
                  $ref: '#/components/schemas/StatusEnum'
                student_ids:
                  type: array
                  items:
                    type: integer
      responses:
        '200':
          description: Student status updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Student'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/system/change-year:
    post:
      tags:
        - System
      summary: Begin the school year process
      description: >-
        Checks the current date against the system's change date and updates the
        academic year accordingly. This process updates the current, next, and
        previous system academic years, updates students to the new academic
        year, and rejects students from the previous academic year.
      operationId: beginSchoolYear
      responses:
        '200':
          description: School year process initiated successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: ok
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/teacher-comments:
    post:
      tags:
        - Teacher Comments
      summary: Add a teacher comment to a student record
      description: >-
        Adds a teacher comment for an existing student and updates the student's
        flag in a transactional manner.
      operationId: createTeacherComment
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/TeacherCommentRequest'
      responses:
        '201':
          description: Teacher comment added successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TeacherComment'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
    get:
      tags:
        - Teacher Comments
      summary: Retrieve teacher comments for a specific student
      description: >-
        Fetches all teacher comments associated with a given student using the
        student's ID.
      operationId: getTeacherCommentsForStudent
      parameters:
        - in: query
          name: student_id
          required: true
          schema:
            type: integer
          description: The ID of the student whose teacher comments will be retrieved.
      responses:
        '200':
          description: Teacher comments retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/TeacherComment'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/students/resend-invitation/{student_id}:
    post:
      tags:
        - Admin/Students
      summary: Resend invitation email for a student
      description: >
        Resends the invitation email for the next academic year to a student.
        This endpoint validates the student's eligibility, retrieves the student
        record and the associated confirmation code, and sends the invitation
        email.
      operationId: resendStudentInvitation
      parameters:
        - in: path
          name: student_id
          required: true
          schema:
            type: integer
          description: The ID of the student for whom the invitation email will be resent.
      responses:
        '204':
          description: No content
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/applicants/{applicantId}/export-pdf:
    post:
      tags:
        - Admin
      summary: Export applicant data to PDF.
      description: Exports the applicant's data to a PDF file.
      operationId: exportPdfApplicationForm
      parameters:
        - name: applicantId
          in: path
          required: true
          schema:
            type: string
          description: The ID of the applicant
      responses:
        '200':
          description: Successfully exported PDF
          content:
            application/pdf:
              schema:
                type: string
                format: binary
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/address-cards/{studentId}/export-pdf:
    post:
      tags:
        - Admin/AddressCard
      summary: Export applicant data to PDF.
      description: Exports the applicant's data to a PDF file.
      operationId: exportPdfAddressCard
      parameters:
        - name: studentId
          in: path
          required: true
          schema:
            type: string
          description: The ID of the applicant
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                academic_year:
                  type: string
                  example: 2023-2024
              required:
                - academic_year
      responses:
        '200':
          description: Successfully exported PDF
          content:
            application/pdf:
              schema:
                type: string
                format: binary
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/student/{student_id}/insurance-month:
    post:
      tags:
        - Student
      summary: Update insurance month for a student
      description: >-
        Validates and updates the insurance month for a given student by their
        ID.
      operationId: updateStudentInsuranceMonth
      parameters:
        - in: path
          name: student_id
          required: true
          schema:
            type: integer
          description: Unique identifier of the student.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateInsuranceMonthRequest'
      responses:
        '200':
          description: Student's insurance month updated successfully.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Student'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /information/get-student/{information_code}:
    get:
      tags:
        - Information
      summary: Retrieve student and admission details by information code
      description: >-
        Fetches student and their admission details based on a unique
        information code and page type.
      operationId: getStudentInformation
      parameters:
        - name: information_code
          in: path
          required: true
          description: Unique information code associated with the student
          schema:
            type: string
        - name: page_type
          in: query
          required: true
          description: Type of page to validate the information code against
          schema:
            type: string
      responses:
        '200':
          description: Student and admission details retrieved successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  student:
                    $ref: '#/components/schemas/Student'
                  admission:
                    $ref: '#/components/schemas/Admission'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /information/user-action/{informationCode}:
    patch:
      tags:
        - Information
      summary: Confirm admission details for a student
      description: >-
        Updates the status of a student's admission process based on the
        provided information code and status.
      operationId: userAction
      parameters:
        - name: informationCode
          in: path
          required: true
          description: >-
            Unique information code associated with the student's admission
            process
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              required:
                - status
                - action_key
              properties:
                status:
                  $ref: '#/components/schemas/StatusEnum'
                action:
                  $ref: '#/components/schemas/ActionKeyEnum'
      responses:
        '200':
          description: Student's admission status updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Student'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /information/address-card/{information_code}:
    post:
      tags:
        - Information
      summary: Create a new address card
      description: Creates a new address card with the provided information.
      operationId: createAddressCard
      parameters:
        - name: information_code
          in: path
          required: true
          description: Unique identifier of the address card
          schema:
            type: string
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/AddressCardRequest'
      responses:
        '201':
          description: Address card created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddressCard'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    get:
      tags:
        - Information
      summary: Retrieve or generate an address card template
      description: >-
        Retrieves an existing address card or generates a template based on the
        provided information code.
      operationId: getAddressCardByInformationCode
      parameters:
        - name: information_code
          in: path
          required: true
          description: The unique information code associated with the address card
          schema:
            type: string
      responses:
        '200':
          description: Address card retrieved or template generated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AddressCard'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /information/documents/{information_code}:
    post:
      tags:
        - Information
      summary: Upload multiple documents by information code
      description: >-
        Uploads multiple document files associated with a given information
        code. Supports uploading a contract, declaration, and official notice.
      operationId: uploadDocumentsByInformationCode
      parameters:
        - name: information_code
          in: path
          required: true
          description: The unique information code associated with the documents
          schema:
            type: string
      requestBody:
        required: true
        content:
          multipart/form-data:
            schema:
              type: object
              properties:
                contract:
                  type: string
                  format: binary
                  description: Contract document file
                declaration:
                  type: string
                  format: binary
                  description: Declaration document file
                officialNotice:
                  type: string
                  format: binary
                  description: Official notice document file
      responses:
        '200':
          description: Documents uploaded successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Documents uploaded successfully
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    get:
      tags:
        - Information
      summary: Retrieve documents by information code
      description: Retrieves a list of documents associated with a given information code.
      operationId: getDocumentsByInformationCode
      parameters:
        - name: information_code
          in: path
          required: true
          description: The unique information code associated with the documents
          schema:
            type: string
      responses:
        '200':
          description: List of documents retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Document'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/student-obligations:
    post:
      tags:
        - Student Obligations
      summary: Create a student obligation
      description: Adds a new student obligation to the database.
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateStudentObligationRequest'
      responses:
        '201':
          description: Student obligation created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StudentObligation'
        '400':
          description: Validation error
        '500':
          description: Internal server error
  /admin/student-obligations/{student_id}:
    get:
      tags:
        - Student Obligations
      summary: Get all obligations for a student
      description: >-
        Fetches all obligations for a given student, optionally filtered by
        academic year.
      operationId: getStudentObligations
      parameters:
        - name: student_id
          in: path
          required: true
          description: Unique identifier of the student
          schema:
            type: integer
        - name: academic_year
          in: query
          required: false
          description: Academic year to filter obligations by (optional)
          schema:
            type: string
      responses:
        '200':
          description: A list of student obligations
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/StudentObligation'
        '400':
          description: Validation error
        '500':
          description: Internal server error
  /admin/student-obligations/total/{student_id}:
    get:
      tags:
        - Student Obligations
      summary: Get total and previous obligations for a student
      description: >-
        Fetches the total and previous financial obligations for a given student
        by their unique identifier.
      operationId: getStudentObligationsTotal
      parameters:
        - name: student_id
          in: path
          required: true
          description: Unique identifier of the student
          schema:
            type: integer
      responses:
        '200':
          description: Total and previous obligations retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ObligationsResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/student-obligations/{obligation_id}:
    delete:
      tags:
        - Student Obligations
      summary: Delete a student obligation if deletable
      description: Deletes a single student obligation if `is_deletable` is set to true.
      operationId: deleteStudentObligation
      parameters:
        - name: obligation_id
          in: path
          required: true
          description: Unique identifier of the student obligation to delete
          schema:
            type: integer
      responses:
        '200':
          description: Obligation deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Obligation deleted successfully.
                  deletedObligation:
                    $ref: '#/components/schemas/StudentObligation'
        '404':
          description: Obligation not found or not deletable
        '500':
          description: Internal server error
  /admin/student-obligation/{obligation_id}:
    patch:
      tags:
        - Student Obligations
      summary: Update a student obligation
      description: Updates the specified fields of a student obligation.
      operationId: updateStudentObligation
      parameters:
        - name: obligation_id
          in: path
          required: true
          description: Unique identifier of the student obligation to update
          schema:
            type: integer
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                obligation_sub_type:
                  type: string
                due_amount_leva:
                  type: number
                  format: double
                due_amount_euro:
                  type: number
                  format: double
                installment_count:
                  type: integer
                comment:
                  type: string
      responses:
        '200':
          description: Student obligation updated successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/StudentObligation'
        '400':
          description: Validation error
        '404':
          description: Obligation not found
        '500':
          description: Internal server error
  /admin/student-obligations/old/{student_id}:
    get:
      tags:
        - Student Obligations
      summary: Get a paginated list of obligations for a student with total count
      description: >-
        Retrieves a paginated list of all obligations associated with a specific
        student, including the total count of obligations.
      operationId: getStudentObligationsPaginatedWithCount
      parameters:
        - name: student_id
          in: path
          required: true
          description: Unique identifier of the student
          schema:
            type: integer
        - name: page
          in: query
          required: false
          description: Page number
          schema:
            type: integer
            default: 1
        - name: pageSize
          in: query
          required: false
          description: Number of records per page
          schema:
            type: integer
            default: 10
      responses:
        '200':
          description: A paginated list of student obligations with total count
          content:
            application/json:
              schema:
                type: object
                properties:
                  data:
                    type: array
                    items:
                      $ref: '#/components/schemas/StudentObligation'
                  totalRecords:
                    type: integer
        '400':
          description: Validation error
        '500':
          description: Internal server error
  /admin/obligation-payment:
    post:
      tags:
        - Payments
      summary: Create a single obligation payment
      description: Creates a new obligation payment record in the database.
      operationId: createObligationPayment
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateObligationPaymentRequest'
      responses:
        '201':
          description: Obligation payment created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ObligationPaymentResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
    put:
      tags:
        - Payments
      summary: Create a single obligation payment
      description: Creates a new obligation payment record in the database.
      operationId: updateObligationPayment
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UpdateObligationPaymentRequest'
      responses:
        '201':
          description: Obligation payment created successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ObligationPaymentResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/obligation-payments/parent-payments/{student_id}:
    get:
      tags:
        - Payments
      summary: Retrieve obligation payments for a specific student
      description: Fetches a list of obligation payments made by or for a specific student.
      operationId: getObligationPayments
      parameters:
        - name: student_id
          in: path
          required: true
          description: Unique identifier of the student
          schema:
            type: integer
      responses:
        '200':
          description: List of obligation payments retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ObligationPayment'
        '400':
          $ref: '#/components/responses/BadRequest'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/obligation-payments/{obligation_id}:
    get:
      tags:
        - Payments
      summary: Get all obligation payments by obligation ID
      description: >-
        Retrieves a list of all obligation payments associated with a specific
        obligation ID.
      operationId: getParentPayments
      parameters:
        - name: obligation_id
          in: path
          required: true
          description: The ID of the obligation to retrieve payments for
          schema:
            type: integer
      responses:
        '200':
          description: A list of obligation payments
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ObligationPayment'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/obligation-payments/child-payments/{payment_id}:
    get:
      tags:
        - Payments
      summary: Retrieve parent payments by payment ID
      description: >-
        Fetches payment details for a specific payment ID, accessible by ADMIN
        and TEACHER roles.
      operationId: getChildPaymentsByPaymentId
      parameters:
        - name: payment_id
          in: path
          required: true
          description: Unique identifier of the payment
          schema:
            type: integer
      responses:
        '200':
          description: Payment details retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ObligationChildPayment'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/obligation-payments/parent-child-payments/{parentPaymentId}:
    get:
      tags:
        - Payments
      summary: Retrieve parent payments by payment ID
      description: >-
        Fetches payment details for a specific payment ID, accessible by ADMIN
        and TEACHER roles.
      operationId: getParentChildPaymentsByParentPaymentId
      parameters:
        - name: parentPaymentId
          in: path
          required: true
          description: Unique identifier of the payment
          schema:
            type: integer
      responses:
        '200':
          description: Payment details retrieved successfully
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/ObligationChildPayment'
        '400':
          $ref: '#/components/responses/BadRequest'
        '401':
          $ref: '#/components/responses/Unauthorized'
        '403':
          $ref: '#/components/responses/Forbidden'
        '404':
          $ref: '#/components/responses/NotFound'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/obligation-sub-types/{parent_type}/{academic_year}:
    get:
      tags:
        - Admin/Obligation Sub Types
      summary: Get all obligation sub types by parent type
      description: Retrieves a list of all obligation sub types filtered by parent type.
      operationId: getObligationSubTypesByParentType
      parameters:
        - name: parent_type
          in: path
          required: true
          description: The parent type of the obligation sub types to retrieve.
          schema:
            type: string
        - name: academic_year
          in: path
          required: true
          description: >-
            The academic year for which the obligation sub types are being
            queried
          schema:
            type: string
      responses:
        '200':
          description: A list of obligation sub types filtered by parent type
          content:
            application/json:
              schema:
                type: array
                items:
                  type: object
                  properties:
                    sub_type_id:
                      type: integer
                      description: The unique identifier of the obligation sub type.
                    name:
                      type: string
                      description: The name of the obligation sub type.
                    academic_year:
                      type: string
                    parent_type:
                      type: string
                      description: The parent type of the obligation sub type.
        '400':
          description: Validation error
        '500':
          description: Internal server error
  /admin/obligation-sub-types:
    post:
      tags:
        - Admin/Obligation Sub Types
      summary: Create a new obligation sub type
      description: Creates a new obligation sub type with the given name and parent type.
      operationId: createObligationSubType
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                name:
                  type: string
                  description: The name of the obligation sub type.
                  example: Transport
                academic_year:
                  type: string
                  description: The academic year of the obligation sub type.
                  example: 2024-2025
                parent_type:
                  type: string
                  description: The parent type of the obligation sub type.
                  example: Транспорт 2 посоки
      responses:
        '201':
          description: Obligation sub type created successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  sub_type_id:
                    type: integer
                    description: The unique identifier of the obligation sub type.
                  name:
                    type: string
                    description: The name of the obligation sub type.
                  academic_year:
                    type: string
                    description: The academic year of the obligation sub type.
                  parent_type:
                    type: string
                    description: The parent type of the obligation sub type.
        '400':
          description: Validation error
        '500':
          description: Internal server error
  /admin/obligation-sub-types/{sub_type_id}:
    delete:
      tags:
        - Admin/Obligation Sub Types
      summary: Delete an obligation sub type
      description: Deletes a single obligation sub type by its ID.
      operationId: deleteObligationSubType
      parameters:
        - name: sub_type_id
          in: path
          required: true
          description: The ID of the obligation sub type to delete.
          schema:
            type: integer
      responses:
        '200':
          description: Obligation sub type deleted successfully
          content:
            application/json:
              schema:
                type: object
                properties:
                  message:
                    type: string
                    example: Obligation sub type deleted successfully.
                  deleted:
                    type: object
                    properties:
                      sub_type_id:
                        type: integer
                      name:
                        type: string
        '400':
          description: Validation error
        '404':
          description: Obligation sub type not found or already deleted
        '500':
          description: Internal server error
  /admin/finances/obligation-sub-types:
    get:
      tags:
        - Finances
      summary: Retrieve obligation sub-types categorized by parent type
      description: >-
        Fetches a list of obligation sub-types categorized into workshops,
        excursions, and others based on the specified academic year.
      operationId: getObligationSubTypes
      parameters:
        - name: academic_year
          in: query
          required: true
          description: >-
            The academic year for which the obligation sub-types are being
            queried
          schema:
            type: string
      responses:
        '200':
          description: Obligation sub-types retrieved successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ObligationSubTypesResponse'
        '400':
          $ref: '#/components/responses/BadRequest'
        '500':
          $ref: '#/components/responses/InternalServerError'
  /admin/finances/get-table:
    get:
      summary: Retrieves the obligation table
      description: >-
        Fetches details of obligations such as fees, meals, transportation, and
        other educational resources for a given academic year and student type.
      operationId: getObligationTable
      tags:
        - Finances
      parameters:
        - in: query
          name: columns
          schema:
            type: array
            items:
              $ref: '#/components/schemas/ObligationTypeEnum'
          required: true
          description: The type of obligation to fetch.
        - in: query
          name: sub_obligation_types
          schema:
            type: object
            properties:
              workshops:
                type: array
                items:
                  type: string
              trips_camps:
                type: array
                items:
                  type: string
              others:
                type: array
                items:
                  type: string
        - in: query
          name: academic_year
          schema:
            type: string
            pattern: ^\d{4}-\d{4}$
          required: true
          description: The academic year for which the obligation details are requested.
        - in: query
          name: student_name
          schema:
            type: string
          required: false
          description: Optional name of the student to filter obligations.
        - in: query
          name: grade_level
          schema:
            type: array
            items:
              $ref: '#/components/schemas/GradeLevelEnum'
          required: false
          description: Filter obligations by grade levels.
        - in: query
          name: grade_level_class
          schema:
            type: array
            items:
              $ref: '#/components/schemas/GradeLevelClassEnum'
          required: false
          description: Filter obligations by class within grade levels.
        - in: query
          name: student_type
          schema:
            type: string
            $ref: '#/components/schemas/StudentTypeEnum'
          required: false
          description: Filter obligations by the type of student.
        - in: query
          name: limit
          schema:
            type: number
            default: 10
            minimum: 1
          required: true
          description: The number of records to return in a single response.
        - in: query
          name: offset
          schema:
            type: number
            default: 0
            minimum: 0
          required: true
      responses:
        '200':
          description: Successfully retrieved payments data.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TableResponse'
        '400':
          description: Bad request due to incorrect or missing parameters.
        '500':
          description: Internal server error.
  /admin/finances/get-payments-table:
    get:
      tags:
        - Finances
      summary: Retrieves payments data based on the provided filters.
      description: >-
        Fetches a table of payments, allowing for filtering by date range,
        payment type, full name, grade, and grade level class with pagination.
      operationId: getPaymentsTable
      parameters:
        - in: query
          name: start_date
          schema:
            type: string
            format: date
          required: false
          description: The start date for filtering payments data.
        - in: query
          name: end_date
          schema:
            type: string
            format: date
          required: false
          description: >-
            The end date for filtering payments data. Required if startDate is
            provided.
        - in: query
          name: payment_type
          schema:
            type: string
            $ref: '#/components/schemas/PaymentTypeEnum'
          required: false
          description: Type of the payment to filter (PAYMENT, REVERSAL).
        - in: query
          name: full_name
          schema:
            type: string
          required: false
          description: The full name associated with the payment for filtering.
        - in: query
          name: current_grade
          schema:
            type: array
            items:
              $ref: '#/components/schemas/GradeLevelEnum'
          required: false
          description: Current grades to filter the payments data.
        - in: query
          name: grade_level_class
          schema:
            type: array
            items:
              $ref: '#/components/schemas/GradeLevelClassEnum'
          required: false
          description: Grade level classes to filter the payments data.
        - in: query
          name: limit
          schema:
            type: integer
            default: 10
            minimum: 1
          required: true
          description: Limit number of payments to fetch.
        - in: query
          name: offset
          schema:
            type: integer
            default: 0
            minimum: 0
          required: true
          description: Offset for pagination.
      responses:
        '200':
          description: Successfully retrieved payments data.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentsResponse'
        '400':
          description: Invalid request parameters.
        '500':
          description: Internal server error.
  /admin/finances/export-payments:
    post:
      tags:
        - Finances
      summary: Export student obligations data to an Excel file
      description: >-
        Allows ADMIN users to export a comprehensive list of student obligations
        into an Excel file.
      operationId: exportStudentObligations
      parameters:
        - in: query
          name: start_date
          schema:
            type: string
            format: date
          required: false
          description: The start date for filtering payments data.
        - in: query
          name: end_date
          schema:
            type: string
            format: date
          required: false
          description: >-
            The end date for filtering payments data. Required if startDate is
            provided.
        - in: query
          name: payment_type
          schema:
            type: string
            $ref: '#/components/schemas/PaymentTypeEnum'
          required: false
          description: Type of the payment to filter (PAYMENT, REVERSAL).
        - in: query
          name: full_name
          schema:
            type: string
          required: false
          description: The full name associated with the payment for filtering.
        - in: query
          name: current_grade
          schema:
            type: array
            items:
              $ref: '#/components/schemas/GradeLevelEnum'
          required: false
          description: Current grades to filter the payments data.
        - in: query
          name: grade_level_class
          schema:
            type: array
            items:
              $ref: '#/components/schemas/GradeLevelClassEnum'
          required: false
          description: Grade level classes to filter the payments data.
      responses:
        '200':
          description: >-
            Excel file containing student obligations data successfully
            generated and downloaded.
          content:
            application/vnd.openxmlformats-officedocument.spreadsheetml.sheet:
              schema:
                type: string
                format: binary
        '500':
          $ref: '#/components/responses/InternalServerError'
tags: []
