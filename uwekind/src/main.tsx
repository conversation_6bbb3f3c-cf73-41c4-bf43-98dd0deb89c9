import { createRoot } from "react-dom/client";
import { <PERSON><PERSON><PERSON><PERSON>outer } from "react-router-dom";

import { CssBaseline, ThemeProvider } from "@mui/material";

import theme from "@constants/theme";
import { StudentsProvider } from "@contexts/AdminStudentsContext";
import { AuthProvider } from "@contexts/AuthContext";
import { OpenAPI } from "@openapi/index";
import { authConfig, authService } from "@utils/virava";

import App from "./App";

import "@fontsource/open-sans/300.css";
import "@fontsource/open-sans/400.css";
import "@fontsource/open-sans/600.css";
import "@fontsource/open-sans/700.css";
import "./i18n";
import "./index.css";

const getConfig = async () => {
  await authService.init(authConfig);

  OpenAPI.BASE = import.meta.env.VITE_OPENAPI_BASE_URL;
};

getConfig()
  .then(() => {
    createRoot(document.getElementById("root")!).render(
      <BrowserRouter>
        <ThemeProvider theme={theme}>
          <CssBaseline />
          <AuthProvider>
            <StudentsProvider>
              <App />
            </StudentsProvider>
          </AuthProvider>
        </ThemeProvider>
      </BrowserRouter>
    );
  })
  .catch((err) => {
    console.error("Failed to initialize auth service", err);
  });
