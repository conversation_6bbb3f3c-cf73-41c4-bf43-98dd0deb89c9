import { CODE_FORBIDDEN, CODE_UNAUTHORIZED } from "@constants/api";
import { ROUTES } from "@constants/routes";
import { OpenAPI } from "@openapi/index";
import { authService } from "@utils/virava";

const setAuthHeader = (
  headers: Record<string, string> = {},
  token: string | null
): Record<string, string> => {
  return token ? { ...headers, Authorization: `Bearer ${token}` } : headers;
};

export async function interceptRequest<Type>(
  serviceFunc: (...args: any[]) => Promise<Type>,
  options: {
    headers?: Record<string, string>;
    getAccessToken: () => string | null;
    onLogout: () => Promise<void>;
    navigate: (path: string) => void;
  },
  ...args: any[]
): Promise<Type> {
  try {
    OpenAPI.HEADERS = setAuthHeader(options.headers, options.getAccessToken());
    return await serviceFunc(...args, options);
  } catch (error) {
    const status = (error as { status: number })?.status;

    if (status === CODE_UNAUTHORIZED) {
      try {
        console.warn("Token expired. Attempting refresh...");

        await authService.updateToken();
        const newToken = options.getAccessToken();

        if (newToken) {
          OpenAPI.HEADERS = setAuthHeader(options.headers, newToken);
          return await serviceFunc(...args, options);
        }

        console.error("Token refresh failed. Logging out...");
        await options.onLogout();
        throw new Error("Authentication expired. Please log in again.");
      } catch (refreshErr) {
        await options.onLogout();
        throw new Error("Session expired. Redirecting to login...");
      }
    }

    if (status === CODE_FORBIDDEN) {
      console.error("Forbidden request. Redirecting to home...");
      options.navigate(ROUTES.ROOT);
      throw new Error("You do not have permission to perform this action.");
    }

    console.error("Unhandled API error:", error);
    throw new Error("Something went wrong. Please try again later.");
  }
}
