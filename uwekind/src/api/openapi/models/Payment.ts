/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { ChildPayment } from './ChildPayment';
export type Payment = {
    payment_id: number;
    amount: number;
    currency: string;
    payment_date: string;
    payment_type: string;
    student_id: number;
    full_name?: string;
    grade_level_class?: string;
    current_grade?: string;
    child: Array<ChildPayment>;
};

