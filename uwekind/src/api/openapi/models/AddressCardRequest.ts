/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
export type AddressCardRequest = {
    name?: string;
    middle_name?: string;
    last_name?: string;
    citizenship?: string;
    date_of_birth?: string;
    identity_number?: string;
    birth_place?: string;
    current_address?: string;
    doctor?: string;
    doctor_phone?: string;
    child_health_data?: string;
    deceased_mother?: boolean;
    mother_name?: string;
    mother_middle_name?: string;
    mother_last_name?: string;
    mother_identity_number?: string;
    mother_phone?: string;
    mother_work_phone?: string;
    mother_email?: string;
    mother_alternative_email?: string;
    mother_work?: string;
    deceased_father?: boolean;
    father_name?: string;
    father_middle_name?: string;
    father_last_name?: string;
    father_identity_number?: string;
    father_phone?: string;
    father_work_phone?: string;
    father_email?: string;
    father_alternative_email?: string;
    father_work?: string;
    picture_agreement?: boolean;
    entertainment_agreement?: boolean;
    leave_alone_agreement?: boolean;
    companions?: string | null;
    installments_count?: string;
};

