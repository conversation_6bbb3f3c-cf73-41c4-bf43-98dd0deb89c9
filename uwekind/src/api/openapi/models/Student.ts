/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { ApplicationTypeEnum } from './ApplicationTypeEnum';
import type { CefrLevelEnum } from './CefrLevelEnum';
import type { GradeLevelClassEnum } from './GradeLevelClassEnum';
import type { GradeLevelEnum } from './GradeLevelEnum';
import type { IbProgramEnum } from './IbProgramEnum';
import type { StatusEnum } from './StatusEnum';
import type { StudentTypeEnum } from './StudentTypeEnum';
export type Student = {
    student_id: number;
    application_type: ApplicationTypeEnum;
    name: string;
    middle_name: string;
    last_name: string;
    date_of_birth: string;
    profile_picture: string;
    citizenship?: string;
    applied_for_grade?: GradeLevelEnum;
    academic_year?: string;
    previous_kindergarten_type?: string;
    family_language: string;
    english_proficiency: CefrLevelEnum;
    german_proficiency: CefrLevelEnum;
    recommendation?: string;
    current_school?: string;
    previous_school?: string;
    ib_program_participation?: IbProgramEnum;
    studied_languages?: string;
    previous_grades_reference?: string;
    current_grades_reference?: string;
    parent_name?: string;
    parent_email?: string;
    parent_phone?: string;
    child_health_data?: string;
    motivation_for_applying: string;
    parent_expectation: string;
    parent_school_activity?: string;
    learned_about_uvekind: string;
    status: StatusEnum;
    psychologist_date?: string;
    admission_id?: number;
    full_name?: string;
    identity_number?: string;
    mother_name?: string;
    mother_email?: string;
    mother_phone?: string;
    father_name?: string;
    father_email?: string;
    father_phone?: string;
    is_tax_payed?: boolean;
    payment_id?: number;
    grade_level_class?: GradeLevelClassEnum;
    applied_for_academic_year?: string;
    current_grade?: GradeLevelEnum;
    student_type?: StudentTypeEnum;
    previous_status?: StatusEnum;
    is_student?: boolean;
    in_process?: boolean;
    public_link?: string;
    created_at?: string;
    updated_at?: string;
    left_school_at?: string;
    has_teacher_comment?: boolean;
    insurance_month?: number;
};

