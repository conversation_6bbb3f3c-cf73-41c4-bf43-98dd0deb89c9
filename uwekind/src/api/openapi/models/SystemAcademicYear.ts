/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
export type SystemAcademicYear = {
    /**
     * The unique identifier for the system academic year.
     */
    system_academic_year_id: number;
    /**
     * The current academic year in the format YYYY/YYYY.
     */
    current_system_academic_year: string;
    /**
     * The previous academic year in the format YYYY/YYYY.
     */
    previous_system_academic_year?: string;
    /**
     * The next academic year in the format YYYY/YYYY.
     */
    next_system_academic_year?: string;
    /**
     * The academic year after the next academic year in the format YYYY/YYYY.
     */
    next_next_system_academic_year?: string;
    /**
     * The timestamp when the current academic year was set.
     */
    change_date: string;
};

