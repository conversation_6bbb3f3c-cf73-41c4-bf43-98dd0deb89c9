/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { ObligationPayment } from './ObligationPayment';
import type { StudentObligation } from './StudentObligation';
export type ObligationPaymentResponse = {
    parentPayment?: ObligationPayment;
    childPayments?: Array<ObligationPayment>;
    updatedObligations?: Array<StudentObligation>;
};

