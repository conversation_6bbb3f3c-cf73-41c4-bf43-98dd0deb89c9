/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { ObligationTypeEnum } from './ObligationTypeEnum';
export type StudentObligation = {
    obligation_id: number;
    obligation_type: ObligationTypeEnum;
    obligation_sub_type?: string | null;
    due_amount_leva: number;
    due_amount_euro: number;
    installment_count?: number;
    balance_leva: number;
    balance_euro: number;
    academic_year: string;
    comment?: string;
    is_deletable: boolean;
    student_id: number;
    created_at: string;
    updated_at: string;
};

