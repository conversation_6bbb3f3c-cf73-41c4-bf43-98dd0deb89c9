/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { DocumentTypeEnum } from './DocumentTypeEnum';
export type Document = {
    /**
     * Unique identifier for the document.
     */
    document_id?: number;
    /**
     * Academic year for which the document is relevant.
     */
    academic_year: string;
    /**
     * Name of the document.
     */
    document_name: string;
    /**
     * Name of the file in the storage bucket.
     */
    bucket_file_name: string;
    /**
     * Foreign key to the student this document belongs to.
     */
    student_id?: number;
    doc_type: DocumentTypeEnum;
    /**
     * Timestamp of when the document was created.
     */
    created_at?: string;
    /**
     * Timestamp of the last update to the document.
     */
    updated_at?: string;
};

