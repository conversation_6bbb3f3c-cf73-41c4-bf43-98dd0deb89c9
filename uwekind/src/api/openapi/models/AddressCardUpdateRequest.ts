/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
export type AddressCardUpdateRequest = {
    academic_year?: string;
    name?: string;
    middle_name?: string;
    last_name?: string;
    citizenship?: string;
    date_of_birth?: string;
    identity_number?: string;
    birth_place?: string;
    current_address?: string;
    doctor?: string;
    doctor_phone?: string;
    child_health_data?: string;
    deceased_mother?: boolean;
    mother_name?: string | null;
    mother_middle_name?: string | null;
    mother_last_name?: string | null;
    mother_identity_number?: string | null;
    mother_phone?: string | null;
    mother_work_phone?: string | null;
    mother_email?: string | null;
    mother_alternative_email?: string | null;
    mother_work?: string | null;
    deceased_father?: boolean;
    father_name?: string | null;
    father_middle_name?: string | null;
    father_last_name?: string | null;
    father_identity_number?: string | null;
    father_phone?: string;
    father_work_phone?: string | null;
    father_email?: string | null;
    father_alternative_email?: string | null;
    father_work?: string | null;
    picture_agreement?: boolean;
    entertainment_agreement?: boolean;
    leave_alone_agreement?: boolean;
    companions?: string | null;
    installments_count?: string;
};

