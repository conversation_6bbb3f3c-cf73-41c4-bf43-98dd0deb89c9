/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CurrencyEnum } from './CurrencyEnum';
import type { PaymentTypeEnum } from './PaymentTypeEnum';
export type UpdateObligationPaymentRequest = {
    student_id?: number;
    parent_payment_id?: number;
    currency: CurrencyEnum;
    payment_date: string;
    payment_type: PaymentTypeEnum;
    payments?: Array<{
        amount?: number;
        obligation_id?: number;
    }>;
};

