/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CurrencyEnum } from './CurrencyEnum';
import type { PaymentTypeEnum } from './PaymentTypeEnum';
export type ObligationPayment = {
    payment_id: number;
    parent_payment_id?: number;
    student_id?: number;
    amount: number;
    currency: CurrencyEnum;
    payment_date?: string;
    payment_type?: PaymentTypeEnum;
    obligation_id: number;
    created_at?: string;
    updated_at?: string;
};

