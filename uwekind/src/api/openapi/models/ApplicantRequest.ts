/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { ApplicationTypeEnum } from './ApplicationTypeEnum';
import type { CefrLevelEnum } from './CefrLevelEnum';
import type { GradeLevelEnum } from './GradeLevelEnum';
import type { IbProgramEnum } from './IbProgramEnum';
export type ApplicantRequest = {
    application_type: ApplicationTypeEnum;
    name: string;
    middle_name: string;
    last_name: string;
    date_of_birth: string;
    profile_picture: Blob;
    citizenship?: string;
    applied_for_grade?: GradeLevelEnum;
    academic_year?: string;
    previous_kindergarten_type?: string;
    family_language: string;
    english_proficiency: CefrLevelEnum;
    german_proficiency: CefrLevelEnum;
    recommendation?: Blob;
    current_school?: string;
    previous_school?: string;
    ib_program_participation?: IbProgramEnum;
    studied_languages?: string;
    previous_grades_reference?: Blob;
    current_grades_reference?: Blob;
    parent_name: string;
    parent_email: string;
    parent_phone: string;
    child_health_data?: string;
    motivation_for_applying: string;
    parent_expectation: string;
    parent_school_activity?: string;
    learned_about_uvekind: string;
};

