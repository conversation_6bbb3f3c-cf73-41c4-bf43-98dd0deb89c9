/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { ObligationTypeEnum } from './ObligationTypeEnum';
export type CreateStudentObligationRequest = {
    obligation_type: ObligationTypeEnum;
    obligation_sub_type?: string;
    due_amount_leva: number;
    due_amount_euro: number;
    installment_count?: number;
    balance_leva: number;
    balance_euro: number;
    academic_year: string;
    student_id: number;
    comment?: string | null;
};

