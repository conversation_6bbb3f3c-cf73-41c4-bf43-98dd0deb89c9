/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { Student } from '../models/Student';
import type { UpdateInsuranceMonthRequest } from '../models/UpdateInsuranceMonthRequest';
import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';
export class StudentService {
    /**
     * Update insurance month for a student
     * Validates and updates the insurance month for a given student by their ID.
     * @param studentId Unique identifier of the student.
     * @param requestBody
     * @returns Student Student's insurance month updated successfully.
     * @throws ApiError
     */
    public static updateStudentInsuranceMonth(
        studentId: number,
        requestBody: UpdateInsuranceMonthRequest,
    ): CancelablePromise<Student> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/admin/student/{student_id}/insurance-month',
            path: {
                'student_id': studentId,
            },
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
}
