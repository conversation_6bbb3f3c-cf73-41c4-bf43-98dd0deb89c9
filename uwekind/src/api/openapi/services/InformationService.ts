/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { ActionKeyEnum } from '../models/ActionKeyEnum';
import type { AddressCard } from '../models/AddressCard';
import type { AddressCardRequest } from '../models/AddressCardRequest';
import type { Admission } from '../models/Admission';
import type { Document } from '../models/Document';
import type { StatusEnum } from '../models/StatusEnum';
import type { Student } from '../models/Student';
import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';
export class InformationService {
    /**
     * Retrieve student and admission details by information code
     * Fetches student and their admission details based on a unique information code and page type.
     * @param informationCode Unique information code associated with the student
     * @param pageType Type of page to validate the information code against
     * @returns any Student and admission details retrieved successfully
     * @throws ApiError
     */
    public static getStudentInformation(
        informationCode: string,
        pageType: string,
    ): CancelablePromise<{
        student?: Student;
        admission?: Admission;
    }> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/information/get-student/{information_code}',
            path: {
                'information_code': informationCode,
            },
            query: {
                'page_type': pageType,
            },
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Confirm admission details for a student
     * Updates the status of a student's admission process based on the provided information code and status.
     * @param informationCode Unique information code associated with the student's admission process
     * @param requestBody
     * @returns Student Student's admission status updated successfully
     * @throws ApiError
     */
    public static userAction(
        informationCode: string,
        requestBody: {
            status: StatusEnum;
            action?: ActionKeyEnum;
        },
    ): CancelablePromise<Student> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/information/user-action/{informationCode}',
            path: {
                'informationCode': informationCode,
            },
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Create a new address card
     * Creates a new address card with the provided information.
     * @param informationCode Unique identifier of the address card
     * @param requestBody
     * @returns AddressCard Address card created successfully
     * @throws ApiError
     */
    public static createAddressCard(
        informationCode: string,
        requestBody: AddressCardRequest,
    ): CancelablePromise<AddressCard> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/information/address-card/{information_code}',
            path: {
                'information_code': informationCode,
            },
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Retrieve or generate an address card template
     * Retrieves an existing address card or generates a template based on the provided information code.
     * @param informationCode The unique information code associated with the address card
     * @returns AddressCard Address card retrieved or template generated successfully
     * @throws ApiError
     */
    public static getAddressCardByInformationCode(
        informationCode: string,
    ): CancelablePromise<AddressCard> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/information/address-card/{information_code}',
            path: {
                'information_code': informationCode,
            },
            errors: {
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Upload multiple documents by information code
     * Uploads multiple document files associated with a given information code. Supports uploading a contract, declaration, and official notice.
     * @param informationCode The unique information code associated with the documents
     * @param formData
     * @returns any Documents uploaded successfully
     * @throws ApiError
     */
    public static uploadDocumentsByInformationCode(
        informationCode: string,
        formData: {
            /**
             * Contract document file
             */
            contract?: Blob;
            /**
             * Declaration document file
             */
            declaration?: Blob;
            /**
             * Official notice document file
             */
            officialNotice?: Blob;
        },
    ): CancelablePromise<{
        message?: string;
    }> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/information/documents/{information_code}',
            path: {
                'information_code': informationCode,
            },
            formData: formData,
            mediaType: 'multipart/form-data',
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Retrieve documents by information code
     * Retrieves a list of documents associated with a given information code.
     * @param informationCode The unique information code associated with the documents
     * @returns Document List of documents retrieved successfully
     * @throws ApiError
     */
    public static getDocumentsByInformationCode(
        informationCode: string,
    ): CancelablePromise<Array<Document>> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/information/documents/{information_code}',
            path: {
                'information_code': informationCode,
            },
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
}
