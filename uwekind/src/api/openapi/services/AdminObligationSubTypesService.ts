/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';
export class AdminObligationSubTypesService {
    /**
     * Get all obligation sub types by parent type
     * Retrieves a list of all obligation sub types filtered by parent type.
     * @param parentType The parent type of the obligation sub types to retrieve.
     * @param academicYear The academic year for which the obligation sub types are being queried
     * @returns any A list of obligation sub types filtered by parent type
     * @throws ApiError
     */
    public static getObligationSubTypesByParentType(
        parentType: string,
        academicYear: string,
    ): CancelablePromise<Array<{
        /**
         * The unique identifier of the obligation sub type.
         */
        sub_type_id?: number;
        /**
         * The name of the obligation sub type.
         */
        name?: string;
        academic_year?: string;
        /**
         * The parent type of the obligation sub type.
         */
        parent_type?: string;
    }>> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/obligation-sub-types/{parent_type}/{academic_year}',
            path: {
                'parent_type': parentType,
                'academic_year': academicYear,
            },
            errors: {
                400: `Validation error`,
                500: `Internal server error`,
            },
        });
    }
    /**
     * Create a new obligation sub type
     * Creates a new obligation sub type with the given name and parent type.
     * @param requestBody
     * @returns any Obligation sub type created successfully
     * @throws ApiError
     */
    public static createObligationSubType(
        requestBody: {
            /**
             * The name of the obligation sub type.
             */
            name?: string;
            /**
             * The academic year of the obligation sub type.
             */
            academic_year?: string;
            /**
             * The parent type of the obligation sub type.
             */
            parent_type?: string;
        },
    ): CancelablePromise<{
        /**
         * The unique identifier of the obligation sub type.
         */
        sub_type_id?: number;
        /**
         * The name of the obligation sub type.
         */
        name?: string;
        /**
         * The academic year of the obligation sub type.
         */
        academic_year?: string;
        /**
         * The parent type of the obligation sub type.
         */
        parent_type?: string;
    }> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/admin/obligation-sub-types',
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `Validation error`,
                500: `Internal server error`,
            },
        });
    }
    /**
     * Delete an obligation sub type
     * Deletes a single obligation sub type by its ID.
     * @param subTypeId The ID of the obligation sub type to delete.
     * @returns any Obligation sub type deleted successfully
     * @throws ApiError
     */
    public static deleteObligationSubType(
        subTypeId: number,
    ): CancelablePromise<{
        message?: string;
        deleted?: {
            sub_type_id?: number;
            name?: string;
        };
    }> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/admin/obligation-sub-types/{sub_type_id}',
            path: {
                'sub_type_id': subTypeId,
            },
            errors: {
                400: `Validation error`,
                404: `Obligation sub type not found or already deleted`,
                500: `Internal server error`,
            },
        });
    }
}
