/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { AddressCard } from '../models/AddressCard';
import type { AddressCardUpdateRequest } from '../models/AddressCardUpdateRequest';
import type { Student } from '../models/Student';
import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';
export class AdminAddressCardService {
    /**
     * Retrieve address cards by student ID
     * Fetches address cards associated with a specific student ID.
     * @param studentId Unique identifier of the student
     * @returns AddressCard Address cards retrieved successfully
     * @throws ApiError
     */
    public static getAddressCardsByStudent(
        studentId: number,
    ): CancelablePromise<Array<AddressCard>> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/address-cards/{studentId}',
            path: {
                'studentId': studentId,
            },
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Resend address cards by student ID
     * Resend address cards associated with a specific student ID.
     * @param studentId Unique identifier of the student
     * @returns void
     * @throws ApiError
     */
    public static resendAddressCardsByStudent(
        studentId: number,
    ): CancelablePromise<void> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/admin/address-cards/{studentId}',
            path: {
                'studentId': studentId,
            },
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Update an address card and student information
     * Updates the address card and student information for a given address card and student ID.
     * @param id Unique identifier of the address card
     * @param studentId Unique identifier of the student
     * @param requestBody
     * @returns any Address card and student updated successfully
     * @throws ApiError
     */
    public static updateAddressCardAndStudent(
        id: string,
        studentId: string,
        requestBody: AddressCardUpdateRequest,
    ): CancelablePromise<{
        addressCard?: AddressCard;
        student?: Student;
    }> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/admin/address-card/{id}/student/{studentId}',
            path: {
                'id': id,
                'studentId': studentId,
            },
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                401: `UNAUTHORIZED`,
                403: `FORBIDDEN`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Export applicant data to PDF.
     * Exports the applicant's data to a PDF file.
     * @param studentId The ID of the applicant
     * @param requestBody
     * @returns binary Successfully exported PDF
     * @throws ApiError
     */
    public static exportPdfAddressCard(
        studentId: string,
        requestBody: {
            academic_year: string;
        },
    ): CancelablePromise<Blob> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/admin/address-cards/{studentId}/export-pdf',
            path: {
                'studentId': studentId,
            },
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
}
