/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CefrLevelEnum } from '../models/CefrLevelEnum';
import type { Document } from '../models/Document';
import type { GradeLevelClassEnum } from '../models/GradeLevelClassEnum';
import type { GradeLevelEnum } from '../models/GradeLevelEnum';
import type { StatusEnum } from '../models/StatusEnum';
import type { Student } from '../models/Student';
import type { StudentResponse } from '../models/StudentResponse';
import type { StudentStatusUpdateRequest } from '../models/StudentStatusUpdateRequest';
import type { StudentStatusUpdateResponse } from '../models/StudentStatusUpdateResponse';
import type { StudentTaxStatusRequest } from '../models/StudentTaxStatusRequest';
import type { StudentTypeEnum } from '../models/StudentTypeEnum';
import type { UpdateStudentStatusRequest } from '../models/UpdateStudentStatusRequest';
import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';
export class AdminStudentsService {
    /**
     * Retrieve a student by ID
     * Fetches a student's data by their unique identifier.
     * @param studentId Unique identifier of the student to retrieve
     * @returns Student Student data retrieved successfully
     * @throws ApiError
     */
    public static getStudentById(
        studentId: number,
    ): CancelablePromise<Student> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/students/{student_id}',
            path: {
                'student_id': studentId,
            },
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Retrieve a list of students with optional filters
     * Fetches a list of students with support for filtering by various criteria and pagination.
     * @param fullName Filter by student's full name
     * @param appliedForGrade Filter by the grade the student has applied for
     * @param currentGrade Filter by the grade the student is currently in
     * @param gradeLevelClass Filter by the class level within the grade
     * @param identityNumber Filter by student's identity number
     * @param academicYear Filter by the academic year
     * @param appliedForAcademicYear Filter by the academic year
     * @param status Filter by the student's application status
     * @param motherName Filter by the mother's name
     * @param motherEmail Filter by the mother's email
     * @param motherPhone Filter by the mother's phone number
     * @param fatherName Filter by the father's name
     * @param fatherEmail Filter by the father's email
     * @param fatherPhone Filter by the father's phone number
     * @param insuranceMonth Insurance month
     * @param isStudent Filter by the is_student
     * @param inProcess Filter by the in_process
     * @param limit Limit the number of returned students per page (default is 10)
     * @param offset Offset for pagination (default is 0)
     * @returns any List of students retrieved successfully
     * @throws ApiError
     */
    public static getStudents(
        fullName?: string,
        appliedForGrade?: Array<GradeLevelEnum>,
        currentGrade?: Array<GradeLevelEnum>,
        gradeLevelClass?: Array<GradeLevelClassEnum>,
        identityNumber?: string,
        academicYear?: string,
        appliedForAcademicYear?: Array<string>,
        status?: Array<StatusEnum>,
        motherName?: string,
        motherEmail?: string,
        motherPhone?: string,
        fatherName?: string,
        fatherEmail?: string,
        fatherPhone?: string,
        insuranceMonth?: number,
        isStudent?: boolean,
        inProcess?: boolean,
        limit: number = 10,
        offset?: number,
    ): CancelablePromise<{
        students?: Array<Student>;
        /**
         * Total count of students matching the filters
         */
        totalCount?: number;
    }> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/students',
            query: {
                'full_name': fullName,
                'applied_for_grade': appliedForGrade,
                'current_grade': currentGrade,
                'grade_level_class': gradeLevelClass,
                'identity_number': identityNumber,
                'academic_year': academicYear,
                'applied_for_academic_year': appliedForAcademicYear,
                'status': status,
                'mother_name': motherName,
                'mother_email': motherEmail,
                'mother_phone': motherPhone,
                'father_name': fatherName,
                'father_email': fatherEmail,
                'father_phone': fatherPhone,
                'insurance_month': insuranceMonth,
                'is_student': isStudent,
                'in_process': inProcess,
                'limit': limit,
                'offset': offset,
            },
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Update student's psychologist date
     * Updates the psychologist date for a specific student by their ID.
     * @param studentId Unique identifier of the student
     * @param requestBody
     * @returns Student Student's psychologist date updated successfully
     * @throws ApiError
     */
    public static updateStudentPsychologistDate(
        studentId: number,
        requestBody: {
            /**
             * New psychologist date for the student
             */
            psychologist_date?: string;
        },
    ): CancelablePromise<Student> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/admin/students/{student_id}/psychologist-date',
            path: {
                'student_id': studentId,
            },
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Update a student's admission ID
     * Updates the admission ID for a specific student by their ID.
     * @param studentId Unique identifier of the student
     * @param requestBody
     * @returns Student Student's admission ID updated successfully
     * @throws ApiError
     */
    public static updateStudentAdmission(
        studentId: number,
        requestBody: {
            /**
             * New admission ID to be associated with the student
             */
            admission_id?: number | null;
        },
    ): CancelablePromise<Student> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/admin/students/{student_id}/admission',
            path: {
                'student_id': studentId,
            },
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Search for student identity numbers
     * Allows searching for student identity numbers based on a provided search string.
     * @param searchString The search string to filter identity numbers
     * @returns any A list of identity numbers matching the search string
     * @throws ApiError
     */
    public static searchIdentityNumbers(
        searchString: string,
    ): CancelablePromise<{
        identityNumbers?: Array<string>;
    }> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/search/identity-numbers',
            query: {
                'searchString': searchString,
            },
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Retrieve a student's details by admission ID
     * Fetches details of a student by their unique admission ID with support for additional filters.
     * @param admissionId Unique identifier of the student's admission
     * @param fullName Filter by student's full name
     * @param appliedForGrade Filter by the grade the student has applied for
     * @param dateOfBirth Filter by the student's date of birth
     * @param parentName Filter by parent's name
     * @param isTaxPayed Filter by tax payment status
     * @param englishProficiency Filter by student's English proficiency level
     * @param germanProficiency Filter by student's German proficiency level
     * @param academicYear Filter by the academic year
     * @param status Filter by the student's application status
     * @param limit Limit the number of returned students per page (default is 10)
     * @param offset Offset for pagination (default is 0)
     * @returns StudentResponse Student details retrieved successfully
     * @throws ApiError
     */
    public static getStudentByAdmissionId(
        admissionId: number,
        fullName?: string,
        appliedForGrade?: Array<GradeLevelEnum>,
        dateOfBirth?: number,
        parentName?: string,
        isTaxPayed?: boolean,
        englishProficiency?: Array<CefrLevelEnum>,
        germanProficiency?: Array<CefrLevelEnum>,
        academicYear?: Array<string>,
        status?: Array<StatusEnum>,
        limit: number = 10,
        offset?: number,
    ): CancelablePromise<StudentResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/students/admision/{admission_id}',
            path: {
                'admission_id': admissionId,
            },
            query: {
                'full_name': fullName,
                'applied_for_grade': appliedForGrade,
                'date_of_birth': dateOfBirth,
                'parent_name': parentName,
                'is_tax_payed': isTaxPayed,
                'english_proficiency': englishProficiency,
                'german_proficiency': germanProficiency,
                'academic_year': academicYear,
                'status': status,
                'limit': limit,
                'offset': offset,
            },
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Search for students by name or admission ID
     * Allows searching for students based on full name or admission ID.
     * @param fullName Full name of the student to search for
     * @param admissionId Admission ID of the student not to search for
     * @returns Student A list of students matching the search criteria
     * @throws ApiError
     */
    public static searchStudents(
        fullName: string,
        admissionId: number,
    ): CancelablePromise<Array<Student>> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/search/students',
            query: {
                'full_name': fullName,
                'admission_id': admissionId,
            },
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Update student status to invited and send an email
     * Updates the status of a student to 'invited' and sends an invitation email if they meet certain criteria.
     * @param studentId Unique identifier of the student to invite
     * @returns any Status updated and email notification sent
     * @throws ApiError
     */
    public static inviteStudent(
        studentId: number,
    ): CancelablePromise<{
        message?: string;
    }> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/admin/students/invite/{studentId}',
            path: {
                'studentId': studentId,
            },
            errors: {
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Update student's tax payment status
     * Updates the tax payment status of a student based on the provided student ID.
     * @param studentId Unique identifier of the student
     * @param requestBody
     * @returns Student Student's tax status updated successfully
     * @throws ApiError
     */
    public static updateStudentTaxStatus(
        studentId: number,
        requestBody: StudentTaxStatusRequest,
    ): CancelablePromise<Student> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/admin/student/{student_id}/tax-status',
            path: {
                'student_id': studentId,
            },
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Delete a student by ID
     * Deletes a student and their associated files by the student's unique identifier.
     * @param studentId Unique identifier of the student to delete
     * @returns any Student and associated files deleted successfully
     * @throws ApiError
     */
    public static deleteStudent(
        studentId: number,
    ): CancelablePromise<{
        message?: string;
    }> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/admin/students/{studentId}',
            path: {
                'studentId': studentId,
            },
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Reject a student by ID
     * Updates the status of a student to 'Отказан' (rejected) by their unique identifier.
     * @param studentId Unique identifier of the student to reject
     * @returns Student Student status updated to 'Отказан'
     * @throws ApiError
     */
    public static rejectStudent(
        studentId: number,
    ): CancelablePromise<Student> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/admin/students/reject/{studentId}',
            path: {
                'studentId': studentId,
            },
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Revert student status
     * Reverts the status of a student to a previous state if certain conditions are met.
     * @param studentId Unique identifier of the student whose status is to be reverted
     * @returns any Student status reverted successfully
     * @throws ApiError
     */
    public static revertStudentStatus(
        studentId: number,
    ): CancelablePromise<{
        message?: string;
        student?: StudentStatusUpdateResponse;
    }> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/admin/student/revert/{student_id}',
            path: {
                'student_id': studentId,
            },
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Send feedback to a student's parent and update student status
     * Sends feedback for a student to their parent's email and updates the student's status.
     * @param studentId Unique identifier of the student
     * @param requestBody
     * @returns Student Feedback sent and student status updated successfully
     * @throws ApiError
     */
    public static sendStudentFeedbackAndUpdateStatus(
        studentId: number,
        requestBody: StudentStatusUpdateRequest,
    ): CancelablePromise<Student> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/admin/student/send-feedback/{student_id}',
            path: {
                'student_id': studentId,
            },
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Update student status to 'Готова документация'
     * Updates the status of a student to 'Готова документация' and sets the updated_at timestamp.
     * @param studentId Unique identifier of the student whose status is to be updated
     * @returns Student Student status updated successfully
     * @throws ApiError
     */
    public static updateStudentStatusToReadyDocumentation(
        studentId: number,
    ): CancelablePromise<Student> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/admin/students/ready-documentation/{student_id}',
            path: {
                'student_id': studentId,
            },
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Get allowed academic years for a student
     * Fetches a list of academic years for which a student is allowed to have address cards.
     * @param studentId Unique identifier of the student
     * @returns any List of allowed academic years retrieved successfully
     * @throws ApiError
     */
    public static getStudentAllowedYears(
        studentId: number,
    ): CancelablePromise<{
        allowedYears?: Array<string>;
    }> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/students/allowed-years/{student_id}',
            path: {
                'student_id': studentId,
            },
            errors: {
                400: `BAD REQUEST`,
                401: `UNAUTHORIZED`,
                403: `FORBIDDEN`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Update a student's information
     * Updates a student's information including their profile picture and personal details.
     * @param studentId The ID of the student to update
     * @param formData
     * @returns Student Student updated successfully
     * @throws ApiError
     */
    public static updateStudent(
        studentId: number,
        formData: {
            name?: string;
            middle_name?: string;
            last_name?: string;
            date_of_birth?: string;
            current_grade?: GradeLevelEnum;
            applied_for_grade?: GradeLevelEnum | null;
            grade_level_class?: GradeLevelClassEnum | null;
            parent_email?: string;
            parent_phone?: string;
            citizenship?: string | null;
            identity_number?: string | null;
            profile_picture?: Blob;
        },
    ): CancelablePromise<Student> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/admin/students/essential/{studentId}',
            path: {
                'studentId': studentId,
            },
            formData: formData,
            mediaType: 'multipart/form-data',
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Update the student type of a student
     * Updates the student type (Ученик, Служебен, Стипендиант) of a student by their ID.
     * @param studentId Unique identifier of the student
     * @param requestBody
     * @returns Student Student type updated successfully
     * @throws ApiError
     */
    public static updateStudentType(
        studentId: number,
        requestBody: {
            student_type?: StudentTypeEnum;
        },
    ): CancelablePromise<Student> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/admin/students/student-type/{student_id}',
            path: {
                'student_id': studentId,
            },
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Send obligations for a specific student
     * Retrieves and sends the total obligations and payments for a specific student by their unique identifier.
     * @param studentId Unique identifier of the student
     * @returns string Obligations sent successfully
     * @throws ApiError
     */
    public static sendStudentObligations(
        studentId: number,
    ): CancelablePromise<string> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/admin/student/send-obligations/{student_id}',
            path: {
                'student_id': studentId,
            },
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Update a student's status
     * Updates the status of a student who is currently in a 'waiting' state. The status can be updated to either 'Отказан' (Rejected) or 'Одобрен' (Approved).
     * @param studentId The unique identifier of the student
     * @param requestBody
     * @returns Student Student status updated successfully
     * @throws ApiError
     */
    public static updateStudentStatus(
        studentId: number,
        requestBody: UpdateStudentStatusRequest,
    ): CancelablePromise<Student> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/admin/student/waiting-action/{student_id}',
            path: {
                'student_id': studentId,
            },
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Update student status to 'В процес на подписване'
     * Updates the status of a student to 'В процес на подписване'.
     * @param studentId Unique identifier of the student whose status is to be updated
     * @returns Student Student status updated successfully
     * @throws ApiError
     */
    public static updateStudentSigningStatus(
        studentId: number,
    ): CancelablePromise<Student> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/admin/students/siging-process/{student_id}',
            path: {
                'student_id': studentId,
            },
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Get all documents for a student
     * Retrieve all documents for a student, with an option to filter by academic year.
     * @param studentId Unique identifier of the student
     * @param academicYear Academic year to filter documents
     * @returns Document A list of documents
     * @throws ApiError
     */
    public static getDocumentsForStudent(
        studentId: number,
        academicYear?: string,
    ): CancelablePromise<Array<Document>> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/students/{studentId}/documents',
            path: {
                'studentId': studentId,
            },
            query: {
                'academic_year': academicYear,
            },
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Update student status to 'Готов за ученик'
     * Updates the status of a student to 'Готов за ученик'.
     * @param studentId Unique identifier of the student whose status is to be updated
     * @returns Student Student status updated successfully
     * @throws ApiError
     */
    public static updateStudentStatusToStudent(
        studentId: number,
    ): CancelablePromise<Student> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/admin/students/student-ready/{studentId}',
            path: {
                'studentId': studentId,
            },
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Reset a student's status
     * Updates the status of a student to 'Одобрен' and optionally progresses them to the next grade based on the provided student ID.
     * @param studentId The unique identifier of the student.
     * @param requestBody
     * @returns Student Student status updated successfully.
     * @throws ApiError
     */
    public static updateStudentStatusToApproved(
        studentId: number,
        requestBody: {
            status: StatusEnum;
            /**
             * Indicates whether the student should progress to the next grade.
             */
            isForNextGrade: boolean;
        },
    ): CancelablePromise<Student> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/admin/students/reset/{student_id}',
            path: {
                'student_id': studentId,
            },
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Update a student's status to 'Одобрен'
     * Updates the status of a student to 'Одобрен' based on the provided student ID.
     * @param requestBody
     * @returns Student Student status updated successfully
     * @throws ApiError
     */
    public static massUpdateStudentsStatusToApproved(
        requestBody: {
            status?: StatusEnum;
            student_ids?: Array<number>;
        },
    ): CancelablePromise<Student> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/admin/students/reset-multiple',
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Resend invitation email for a student
     * Resends the invitation email for the next academic year to a student. This endpoint validates the student's eligibility, retrieves the student record and the associated confirmation code, and sends the invitation email.
     *
     * @param studentId The ID of the student for whom the invitation email will be resent.
     * @returns void
     * @throws ApiError
     */
    public static resendStudentInvitation(
        studentId: number,
    ): CancelablePromise<void> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/admin/students/resend-invitation/{student_id}',
            path: {
                'student_id': studentId,
            },
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
}
