/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { AdmissionTypeEnum } from '../models/AdmissionTypeEnum';
import type { Subject } from '../models/Subject';
import type { SubjectRequest } from '../models/SubjectRequest';
import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';
export class AdminSubjectService {
    /**
     * Create a new subject
     * Creates a new subject with the provided name and type.
     * @param requestBody
     * @returns Subject Subject created successfully
     * @throws ApiError
     */
    public static createSubject(
        requestBody: SubjectRequest,
    ): CancelablePromise<Subject> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/admin/subjects',
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Retrieve subjects by type
     * Fetches a list of subjects filtered by their type.
     * @param type Type of the subjects to filter by
     * @returns Subject Subjects fetched successfully
     * @throws ApiError
     */
    public static getSubjectsByType(
        type: AdmissionTypeEnum,
    ): CancelablePromise<Array<Subject>> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/subjects',
            query: {
                'type': type,
            },
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Delete a subject by ID
     * Deletes an existing subject by its unique identifier.
     * @param subjectId Unique identifier of the subject to delete
     * @returns Subject Subject deleted successfully
     * @throws ApiError
     */
    public static deleteSubject(
        subjectId: number,
    ): CancelablePromise<Subject> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/admin/subjects/{subjectId}',
            path: {
                'subjectId': subjectId,
            },
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
}
