/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { Admission } from '../models/Admission';
import type { AdmissionRequest } from '../models/AdmissionRequest';
import type { AdmissionsListResponse } from '../models/AdmissionsListResponse';
import type { AdmissionTypeEnum } from '../models/AdmissionTypeEnum';
import type { AdmissionUpdateRequest } from '../models/AdmissionUpdateRequest';
import type { AssignStudentsRequest } from '../models/AssignStudentsRequest';
import type { Student } from '../models/Student';
import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';
export class AdminAdmissionsService {
    /**
     * Retrieve a list of admissions with optional filters
     * Admin endpoint to fetch a list of admissions with support for filtering by primary teacher, primary psychologist, admission date range, and admission type.
     * @param limit Limit the number of admissions to retrieve
     * @param offset Offset for pagination
     * @param primaryTeacher Filter by primary teacher's name
     * @param primaryPsychologist Filter by primary psychologist's name
     * @param startAdmissionDate Start date for admission date range filter
     * @param endAdmissionDate End date for admission date range filter
     * @param admissionType Filter by admission type
     * @returns AdmissionsListResponse List of admissions retrieved successfully
     * @throws ApiError
     */
    public static adminListAdmissions(
        limit: number,
        offset: number,
        primaryTeacher?: string,
        primaryPsychologist?: string,
        startAdmissionDate?: string,
        endAdmissionDate?: string,
        admissionType?: Array<AdmissionTypeEnum>,
    ): CancelablePromise<AdmissionsListResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/admissions',
            query: {
                'limit': limit,
                'offset': offset,
                'primary_teacher': primaryTeacher,
                'primary_psychologist': primaryPsychologist,
                'start_admission_date': startAdmissionDate,
                'end_admission_date': endAdmissionDate,
                'admission_type': admissionType,
            },
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Create a new admission record
     * Creates a new admission record with the provided details.
     * @param requestBody
     * @returns Admission Admission created successfully
     * @throws ApiError
     */
    public static adminCreateAdmission(
        requestBody: AdmissionRequest,
    ): CancelablePromise<Admission> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/admin/admissions',
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Retrieve admission details by ID
     * Admin endpoint to fetch the details of an admission by its unique identifier.
     * @param admissionId Unique identifier of the admission to retrieve
     * @returns Admission Admission details retrieved successfully
     * @throws ApiError
     */
    public static adminGetAdmissionById(
        admissionId: number,
    ): CancelablePromise<Admission> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/admissions/{admission_id}',
            path: {
                'admission_id': admissionId,
            },
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Delete an admission and its materials
     * Deletes an admission record and its associated materials by the admission's unique identifier.
     * @param admissionId Unique identifier of the admission to delete
     * @returns any Admission and associated materials deleted successfully
     * @throws ApiError
     */
    public static deleteAdmissionWithMaterials(
        admissionId: number,
    ): CancelablePromise<{
        message?: string;
    }> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/admin/admissions/{admission_id}',
            path: {
                'admission_id': admissionId,
            },
            errors: {
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Update an admission by ID
     * Admin endpoint to update an admission record by its unique identifier.
     * @param admissionId Unique identifier of the admission to update
     * @param requestBody
     * @returns Admission Admission updated successfully
     * @throws ApiError
     */
    public static adminUpdateAdmission(
        admissionId: number,
        requestBody: AdmissionUpdateRequest,
    ): CancelablePromise<Admission> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/admin/admissions/{admission_id}',
            path: {
                'admission_id': admissionId,
            },
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Retrieve possible students for a given admission
     * Fetches a list of possible students based on the admission type associated with the provided admission ID. Accessible by ADMIN and TEACHER roles.
     * @param admissionId The ID of the admission to retrieve possible students for
     * @returns Student A list of possible students retrieved successfully
     * @throws ApiError
     */
    public static getAdmissionPossibleStudents(
        admissionId: number,
    ): CancelablePromise<Array<Student>> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/admission/possible-stundents/{admission_id}',
            path: {
                'admission_id': admissionId,
            },
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Assign students to an admission
     * Assigns a list of students to a specific admission based on the admission ID. Accessible by ADMIN and TEACHER roles.
     * @param admissionId The ID of the admission to assign students to
     * @param requestBody
     * @returns Student Students assigned to admission successfully
     * @throws ApiError
     */
    public static assignStudentsToAdmission(
        admissionId: number,
        requestBody: AssignStudentsRequest,
    ): CancelablePromise<Array<Student>> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/admin/admission/assign-students/{admission_id}',
            path: {
                'admission_id': admissionId,
            },
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
}
