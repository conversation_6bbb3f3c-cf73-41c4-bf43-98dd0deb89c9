/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { Document } from '../models/Document';
import type { DocumentTypeEnum } from '../models/DocumentTypeEnum';
import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';
export class AdminDocumentsService {
    /**
     * Create a new document and upload it
     * Create a new document with details and upload the document file.
     * @param formData
     * @returns Document Document created successfully
     * @throws ApiError
     */
    public static createDocument(
        formData: {
            academic_year: string;
            student_id: number;
            doc_type: DocumentTypeEnum;
            document_file: Blob;
        },
    ): CancelablePromise<Document> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/admin/documents',
            formData: formData,
            mediaType: 'multipart/form-data',
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Delete a document
     * Deletes a single document from the database and storage.
     * @param documentId The ID of the document to delete
     * @returns any Document deleted successfully
     * @throws ApiError
     */
    public static deleteDocument(
        documentId: number,
    ): CancelablePromise<{
        message?: string;
    }> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/admin/documents/{documentId}',
            path: {
                'documentId': documentId,
            },
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
}
