/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { AdmissionMaterials } from '../models/AdmissionMaterials';
import type { AdmissionMaterialsRequest } from '../models/AdmissionMaterialsRequest';
import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';
export class AdminAdmissionMaterialsService {
    /**
     * Upload admission materials
     * Allows for the uploading of multiple admission materials associated with an admission ID.
     * @param formData
     * @returns AdmissionMaterials Admission materials uploaded successfully
     * @throws ApiError
     */
    public static uploadAdmissionMaterials(
        formData: AdmissionMaterialsRequest,
    ): CancelablePromise<Array<AdmissionMaterials>> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/admin/admission-materials',
            formData: formData,
            mediaType: 'multipart/form-data',
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Retrieve admission materials by admission ID
     * Fetches a list of admission materials associated with a specific admission ID, including pre-signed URLs for file access.
     * @param admissionId The ID of the admission for which materials are being retrieved.
     * @returns AdmissionMaterials List of admission materials retrieved successfully
     * @throws ApiError
     */
    public static getAdmissionMaterials(
        admissionId: number,
    ): CancelablePromise<Array<AdmissionMaterials>> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/admission-materials/{admission_id}',
            path: {
                'admission_id': admissionId,
            },
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Delete an admission material
     * Deletes a specific admission material and its associated file from the storage based on the admission ID and material ID.
     * @param admissionId The ID of the admission to which the material is related.
     * @param materialId The unique identifier for the admission material to be deleted.
     * @returns any Admission material and associated file deleted successfully.
     * @throws ApiError
     */
    public static deleteAdmissionMaterial(
        admissionId: number,
        materialId: number,
    ): CancelablePromise<{
        message?: string;
    }> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/admin/admission-materials/{admission_id}/{material_id}',
            path: {
                'admission_id': admissionId,
                'material_id': materialId,
            },
            errors: {
                400: `BAD REQUEST`,
                404: `Admission material not found.`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
}
