/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { Applicant } from '../models/Applicant';
import type { ApplicantRequest } from '../models/ApplicantRequest';
import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';
export class ApplicantsService {
    /**
     * Create a new applicant
     * Create a new applicant with their details and upload documents.
     * @param formData
     * @returns Applicant Applicant created successfully
     * @throws ApiError
     */
    public static createApplicant(
        formData: ApplicantRequest,
    ): CancelablePromise<Applicant> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/applicants',
            formData: formData,
            mediaType: 'multipart/form-data',
            errors: {
                400: `BAD REQUEST`,
                401: `UNAUTHORIZED`,
                403: `FORBIDDEN`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Retrieve the next two system academic years
     * Fetches the next two system academic years based on the current system settings.
     * @returns any Successfully retrieved the next two system academic years
     * @throws ApiError
     */
    public static getSystemAcademicYears(): CancelablePromise<{
        allowedYears?: Array<string>;
    }> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/applicants/get-years',
            errors: {
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
}
