/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { SystemAcademicYear } from '../models/SystemAcademicYear';
import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';
export class AdminSystemService {
    /**
     * Get current system academic year
     * Retrieves the current system academic year.
     * @returns SystemAcademicYear Current system academic year retrieved successfully
     * @throws ApiError
     */
    public static getCurrentSystemAcademicYear(): CancelablePromise<SystemAcademicYear> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/system-academic-year/current',
            errors: {
                404: `No current academic year found`,
                500: `Internal Server Error`,
            },
        });
    }
}
