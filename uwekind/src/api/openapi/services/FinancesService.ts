/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { GradeLevelClassEnum } from '../models/GradeLevelClassEnum';
import type { GradeLevelEnum } from '../models/GradeLevelEnum';
import type { ObligationSubTypesResponse } from '../models/ObligationSubTypesResponse';
import type { ObligationTypeEnum } from '../models/ObligationTypeEnum';
import type { PaymentsResponse } from '../models/PaymentsResponse';
import type { PaymentTypeEnum } from '../models/PaymentTypeEnum';
import type { StudentTypeEnum } from '../models/StudentTypeEnum';
import type { TableResponse } from '../models/TableResponse';
import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';
export class FinancesService {
    /**
     * Retrieve obligation sub-types categorized by parent type
     * Fetches a list of obligation sub-types categorized into workshops, excursions, and others based on the specified academic year.
     * @param academicYear The academic year for which the obligation sub-types are being queried
     * @returns ObligationSubTypesResponse Obligation sub-types retrieved successfully
     * @throws ApiError
     */
    public static getObligationSubTypes(
        academicYear: string,
    ): CancelablePromise<ObligationSubTypesResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/finances/obligation-sub-types',
            query: {
                'academic_year': academicYear,
            },
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Retrieves the obligation table
     * Fetches details of obligations such as fees, meals, transportation, and other educational resources for a given academic year and student type.
     * @param columns The type of obligation to fetch.
     * @param academicYear The academic year for which the obligation details are requested.
     * @param subObligationTypes
     * @param studentName Optional name of the student to filter obligations.
     * @param gradeLevel Filter obligations by grade levels.
     * @param gradeLevelClass Filter obligations by class within grade levels.
     * @param studentType Filter obligations by the type of student.
     * @param limit The number of records to return in a single response.
     * @param offset
     * @returns TableResponse Successfully retrieved payments data.
     * @throws ApiError
     */
    public static getObligationTable(
        columns: Array<ObligationTypeEnum>,
        academicYear: string,
        subObligationTypes?: {
            workshops?: Array<string>;
            trips_camps?: Array<string>;
            others?: Array<string>;
        },
        studentName?: string,
        gradeLevel?: Array<GradeLevelEnum>,
        gradeLevelClass?: Array<GradeLevelClassEnum>,
        studentType?: StudentTypeEnum,
        limit: number = 10,
        offset: number,
    ): CancelablePromise<TableResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/finances/get-table',
            query: {
                'columns': columns,
                'sub_obligation_types': subObligationTypes,
                'academic_year': academicYear,
                'student_name': studentName,
                'grade_level': gradeLevel,
                'grade_level_class': gradeLevelClass,
                'student_type': studentType,
                'limit': limit,
                'offset': offset,
            },
            errors: {
                400: `Bad request due to incorrect or missing parameters.`,
                500: `Internal server error.`,
            },
        });
    }
    /**
     * Retrieves payments data based on the provided filters.
     * Fetches a table of payments, allowing for filtering by date range, payment type, full name, grade, and grade level class with pagination.
     * @param startDate The start date for filtering payments data.
     * @param endDate The end date for filtering payments data. Required if startDate is provided.
     * @param paymentType Type of the payment to filter (PAYMENT, REVERSAL).
     * @param fullName The full name associated with the payment for filtering.
     * @param currentGrade Current grades to filter the payments data.
     * @param gradeLevelClass Grade level classes to filter the payments data.
     * @param limit Limit number of payments to fetch.
     * @param offset Offset for pagination.
     * @returns PaymentsResponse Successfully retrieved payments data.
     * @throws ApiError
     */
    public static getPaymentsTable(
        startDate?: string,
        endDate?: string,
        paymentType?: PaymentTypeEnum,
        fullName?: string,
        currentGrade?: Array<GradeLevelEnum>,
        gradeLevelClass?: Array<GradeLevelClassEnum>,
        limit: number = 10,
        offset: number,
    ): CancelablePromise<PaymentsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/finances/get-payments-table',
            query: {
                'start_date': startDate,
                'end_date': endDate,
                'payment_type': paymentType,
                'full_name': fullName,
                'current_grade': currentGrade,
                'grade_level_class': gradeLevelClass,
                'limit': limit,
                'offset': offset,
            },
            errors: {
                400: `Invalid request parameters.`,
                500: `Internal server error.`,
            },
        });
    }
    /**
     * Export student obligations data to an Excel file
     * Allows ADMIN users to export a comprehensive list of student obligations into an Excel file.
     * @param startDate The start date for filtering payments data.
     * @param endDate The end date for filtering payments data. Required if startDate is provided.
     * @param paymentType Type of the payment to filter (PAYMENT, REVERSAL).
     * @param fullName The full name associated with the payment for filtering.
     * @param currentGrade Current grades to filter the payments data.
     * @param gradeLevelClass Grade level classes to filter the payments data.
     * @returns binary Excel file containing student obligations data successfully generated and downloaded.
     * @throws ApiError
     */
    public static exportStudentObligations(
        startDate?: string,
        endDate?: string,
        paymentType?: PaymentTypeEnum,
        fullName?: string,
        currentGrade?: Array<GradeLevelEnum>,
        gradeLevelClass?: Array<GradeLevelClassEnum>,
    ): CancelablePromise<Blob> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/admin/finances/export-payments',
            query: {
                'start_date': startDate,
                'end_date': endDate,
                'payment_type': paymentType,
                'full_name': fullName,
                'current_grade': currentGrade,
                'grade_level_class': gradeLevelClass,
            },
            errors: {
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
}
