/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { Applicant } from '../models/Applicant';
import type { ApplicantConversionRequest } from '../models/ApplicantConversionRequest';
import type { ApplicantConversionResponse } from '../models/ApplicantConversionResponse';
import type { ApplicantsStatusRequest } from '../models/ApplicantsStatusRequest';
import type { ApplicantUpdateRequest } from '../models/ApplicantUpdateRequest';
import type { DeleteApplicantResponse } from '../models/DeleteApplicantResponse';
import type { DeleteMultipleApplicantsRequest } from '../models/DeleteMultipleApplicantsRequest';
import type { GradeLevelEnum } from '../models/GradeLevelEnum';
import type { PaginatedApplicantsResponse } from '../models/PaginatedApplicantsResponse';
import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';
export class AdminService {
    /**
     * Delete an applicant by ID
     * Deletes an applicant from the system using their unique ID.
     * @param id Unique ID of the applicant to delete
     * @returns DeleteApplicantResponse Applicant deleted successfully
     * @throws ApiError
     */
    public static deleteApplicant(
        id: number,
    ): CancelablePromise<DeleteApplicantResponse> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/admin/applicants/{id}',
            path: {
                'id': id,
            },
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Update an applicant's information
     * Updates the information of an applicant identified by their unique ID.
     * @param id Unique identifier of the applicant to update
     * @param requestBody
     * @returns Applicant Applicant updated successfully
     * @throws ApiError
     */
    public static updateApplicant(
        id: number,
        requestBody: ApplicantUpdateRequest,
    ): CancelablePromise<Applicant> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/admin/applicants/{id}',
            path: {
                'id': id,
            },
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Retrieve applicant information by ID
     * Fetches detailed information of a specific applicant using their unique ID.
     * @param applicantId Unique identifier of the applicant
     * @returns Applicant Applicant information retrieved successfully
     * @throws ApiError
     */
    public static getApplicantById(
        applicantId: number,
    ): CancelablePromise<Applicant> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/applicant/{applicant_id}',
            path: {
                'applicant_id': applicantId,
            },
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Delete multiple applicants
     * Deletes multiple applicants from the system using a list of unique IDs provided in the request body.
     * @param requestBody A JSON object containing an array of applicant IDs to delete
     * @returns any Applicants deleted successfully.
     * @throws ApiError
     */
    public static adminDeleteMultipleApplicants(
        requestBody: DeleteMultipleApplicantsRequest,
    ): CancelablePromise<{
        message?: string;
    }> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/admin/applicants',
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                404: `No applicants found or deleted with the provided IDs.`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Retrieve a paginated list of applicants with optional filters
     * Admin endpoint to fetch a list of applicants with support for filtering by name, date of birth range, grade level, and pagination.
     * @param name Filter by applicant's name
     * @param middleName Filter by applicant's middle name
     * @param lastName Filter by applicant's last name
     * @param startDateOfBirth Filter by the start range of applicant's date of birth
     * @param endDateOfBirth Filter by the end range of applicant's date of birth
     * @param appliedForGrade Filter by applicant's grade level
     * @param limit Limit the number of applicants to retrieve
     * @param offset Pagination offset
     * @returns PaginatedApplicantsResponse Paginated list of applicants retrieved successfully
     * @throws ApiError
     */
    public static adminGetPaginatedApplicants(
        name?: string,
        middleName?: string,
        lastName?: string,
        startDateOfBirth?: string,
        endDateOfBirth?: string,
        appliedForGrade?: Array<GradeLevelEnum>,
        limit: number = 10,
        offset?: number,
    ): CancelablePromise<PaginatedApplicantsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/applicants',
            query: {
                'name': name,
                'middle_name': middleName,
                'last_name': lastName,
                'start_date_of_birth': startDateOfBirth,
                'end_date_of_birth': endDateOfBirth,
                'applied_for_grade': appliedForGrade,
                'limit': limit,
                'offset': offset,
            },
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Convert an applicant to a student
     * Accepts an applicant by their ID, assigns them a status, converts them to a student, and deletes the applicant record.
     * @param requestBody
     * @returns ApplicantConversionResponse Applicant converted to student and deleted.
     * @throws ApiError
     */
    public static convertApplicant(
        requestBody: ApplicantConversionRequest,
    ): CancelablePromise<ApplicantConversionResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/admin/applicants/convert',
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Convert multiple applicants to students
     * Accepts multiple applicants by their IDs, assigns them a status, and converts them to students.
     * @param requestBody
     * @returns any Applicants converted to students successfully.
     * @throws ApiError
     */
    public static convertMultipleApplicants(
        requestBody: ApplicantsStatusRequest,
    ): CancelablePromise<{
        message?: string;
        /**
         * Number of applicants converted to students.
         */
        converted?: number;
    }> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/admin/applicants/convert-multiple',
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Export applicant data to PDF.
     * Exports the applicant's data to a PDF file.
     * @param applicantId The ID of the applicant
     * @returns binary Successfully exported PDF
     * @throws ApiError
     */
    public static exportPdfApplicationForm(
        applicantId: string,
    ): CancelablePromise<Blob> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/admin/applicants/{applicantId}/export-pdf',
            path: {
                'applicantId': applicantId,
            },
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
}
