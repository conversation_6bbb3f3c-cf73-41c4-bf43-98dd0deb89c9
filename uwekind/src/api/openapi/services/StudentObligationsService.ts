/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreateStudentObligationRequest } from '../models/CreateStudentObligationRequest';
import type { ObligationsResponse } from '../models/ObligationsResponse';
import type { StudentObligation } from '../models/StudentObligation';
import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';
export class StudentObligationsService {
    /**
     * Create a student obligation
     * Adds a new student obligation to the database.
     * @param requestBody
     * @returns StudentObligation Student obligation created successfully
     * @throws ApiError
     */
    public static postAdminStudentObligations(
        requestBody: CreateStudentObligationRequest,
    ): CancelablePromise<StudentObligation> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/admin/student-obligations',
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `Validation error`,
                500: `Internal server error`,
            },
        });
    }
    /**
     * Get all obligations for a student
     * Fetches all obligations for a given student, optionally filtered by academic year.
     * @param studentId Unique identifier of the student
     * @param academicYear Academic year to filter obligations by (optional)
     * @returns StudentObligation A list of student obligations
     * @throws ApiError
     */
    public static getStudentObligations(
        studentId: number,
        academicYear?: string,
    ): CancelablePromise<Array<StudentObligation>> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/student-obligations/{student_id}',
            path: {
                'student_id': studentId,
            },
            query: {
                'academic_year': academicYear,
            },
            errors: {
                400: `Validation error`,
                500: `Internal server error`,
            },
        });
    }
    /**
     * Get total and previous obligations for a student
     * Fetches the total and previous financial obligations for a given student by their unique identifier.
     * @param studentId Unique identifier of the student
     * @returns ObligationsResponse Total and previous obligations retrieved successfully
     * @throws ApiError
     */
    public static getStudentObligationsTotal(
        studentId: number,
    ): CancelablePromise<ObligationsResponse> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/student-obligations/total/{student_id}',
            path: {
                'student_id': studentId,
            },
            errors: {
                400: `BAD REQUEST`,
                401: `UNAUTHORIZED`,
                403: `FORBIDDEN`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Delete a student obligation if deletable
     * Deletes a single student obligation if `is_deletable` is set to true.
     * @param obligationId Unique identifier of the student obligation to delete
     * @returns any Obligation deleted successfully
     * @throws ApiError
     */
    public static deleteStudentObligation(
        obligationId: number,
    ): CancelablePromise<{
        message?: string;
        deletedObligation?: StudentObligation;
    }> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/admin/student-obligations/{obligation_id}',
            path: {
                'obligation_id': obligationId,
            },
            errors: {
                404: `Obligation not found or not deletable`,
                500: `Internal server error`,
            },
        });
    }
    /**
     * Update a student obligation
     * Updates the specified fields of a student obligation.
     * @param obligationId Unique identifier of the student obligation to update
     * @param requestBody
     * @returns StudentObligation Student obligation updated successfully
     * @throws ApiError
     */
    public static updateStudentObligation(
        obligationId: number,
        requestBody: {
            obligation_sub_type?: string;
            due_amount_leva?: number;
            due_amount_euro?: number;
            installment_count?: number;
            comment?: string;
        },
    ): CancelablePromise<StudentObligation> {
        return __request(OpenAPI, {
            method: 'PATCH',
            url: '/admin/student-obligation/{obligation_id}',
            path: {
                'obligation_id': obligationId,
            },
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `Validation error`,
                404: `Obligation not found`,
                500: `Internal server error`,
            },
        });
    }
    /**
     * Get a paginated list of obligations for a student with total count
     * Retrieves a paginated list of all obligations associated with a specific student, including the total count of obligations.
     * @param studentId Unique identifier of the student
     * @param page Page number
     * @param pageSize Number of records per page
     * @returns any A paginated list of student obligations with total count
     * @throws ApiError
     */
    public static getStudentObligationsPaginatedWithCount(
        studentId: number,
        page: number = 1,
        pageSize: number = 10,
    ): CancelablePromise<{
        data?: Array<StudentObligation>;
        totalRecords?: number;
    }> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/student-obligations/old/{student_id}',
            path: {
                'student_id': studentId,
            },
            query: {
                'page': page,
                'pageSize': pageSize,
            },
            errors: {
                400: `Validation error`,
                500: `Internal server error`,
            },
        });
    }
}
