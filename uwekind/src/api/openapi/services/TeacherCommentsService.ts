/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { TeacherComment } from '../models/TeacherComment';
import type { TeacherCommentRequest } from '../models/TeacherCommentRequest';
import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';
export class TeacherCommentsService {
    /**
     * Add a teacher comment to a student record
     * Adds a teacher comment for an existing student and updates the student's flag in a transactional manner.
     * @param requestBody
     * @returns TeacherComment Teacher comment added successfully
     * @throws ApiError
     */
    public static createTeacherComment(
        requestBody: TeacherCommentRequest,
    ): CancelablePromise<TeacherComment> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/admin/teacher-comments',
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Retrieve teacher comments for a specific student
     * Fetches all teacher comments associated with a given student using the student's ID.
     * @param studentId The ID of the student whose teacher comments will be retrieved.
     * @returns TeacherComment Teacher comments retrieved successfully
     * @throws ApiError
     */
    public static getTeacherCommentsForStudent(
        studentId: number,
    ): CancelablePromise<Array<TeacherComment>> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/teacher-comments',
            query: {
                'student_id': studentId,
            },
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
}
