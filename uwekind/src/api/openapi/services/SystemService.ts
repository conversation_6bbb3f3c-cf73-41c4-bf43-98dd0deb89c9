/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';
export class SystemService {
    /**
     * Begin the school year process
     * Checks the current date against the system's change date and updates the academic year accordingly. This process updates the current, next, and previous system academic years, updates students to the new academic year, and rejects students from the previous academic year.
     * @returns any School year process initiated successfully
     * @throws ApiError
     */
    public static beginSchoolYear(): CancelablePromise<{
        message?: string;
    }> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/admin/system/change-year',
            errors: {
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
}
