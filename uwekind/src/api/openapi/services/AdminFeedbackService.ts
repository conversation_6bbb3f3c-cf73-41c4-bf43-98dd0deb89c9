/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { Feedback } from '../models/Feedback';
import type { FeedbackRequest } from '../models/FeedbackRequest';
import type { FeedbackUpdateRequest } from '../models/FeedbackUpdateRequest';
import type { FeedbackWeightUpdateRequest } from '../models/FeedbackWeightUpdateRequest';
import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';
export class AdminFeedbackService {
    /**
     * Create new feedback
     * Creates a new feedback entry for a student.
     * @param requestBody
     * @returns Feedback Feedback created successfully
     * @throws ApiError
     */
    public static createFeedback(
        requestBody: FeedbackRequest,
    ): CancelablePromise<Feedback> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/admin/feedback',
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Update existing feedback
     * Updates an existing feedback entry for a student based on the provided feedback ID.
     * @param feedbackId Unique identifier of the feedback to update
     * @param requestBody
     * @returns Feedback Feedback updated successfully
     * @throws ApiError
     */
    public static updateFeedback(
        feedbackId: number,
        requestBody: FeedbackUpdateRequest,
    ): CancelablePromise<Feedback> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/admin/feedback/{feedback_id}',
            path: {
                'feedback_id': feedbackId,
            },
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Delete feedback by ID
     * Deletes an existing feedback entry by its unique identifier.
     * @param feedbackId Unique identifier of the feedback to delete
     * @returns any Feedback deleted successfully
     * @throws ApiError
     */
    public static deleteFeedback(
        feedbackId: number,
    ): CancelablePromise<{
        message?: string;
        deletedFeedback?: Feedback;
    }> {
        return __request(OpenAPI, {
            method: 'DELETE',
            url: '/admin/feedback/{feedback_id}',
            path: {
                'feedback_id': feedbackId,
            },
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Get feedback for a specific student
     * Retrieves all feedback entries associated with a given student ID.
     * @param studentId Unique identifier of the student to retrieve feedback for
     * @returns Feedback Feedback entries retrieved successfully
     * @throws ApiError
     */
    public static getFeedbackByStudentId(
        studentId: number,
    ): CancelablePromise<Array<Feedback>> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/feedback/{student_id}',
            path: {
                'student_id': studentId,
            },
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Reorder feedback weights
     * Updates the weights of multiple feedback entries in a batch operation.
     * @param requestBody
     * @returns Feedback Feedback weights updated successfully
     * @throws ApiError
     */
    public static reorderFeedback(
        requestBody: Array<FeedbackWeightUpdateRequest>,
    ): CancelablePromise<Array<Feedback>> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/admin/feedback/reorder',
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
}
