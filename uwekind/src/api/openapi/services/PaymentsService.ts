/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
import type { CreateObligationPaymentRequest } from '../models/CreateObligationPaymentRequest';
import type { ObligationChildPayment } from '../models/ObligationChildPayment';
import type { ObligationPayment } from '../models/ObligationPayment';
import type { ObligationPaymentResponse } from '../models/ObligationPaymentResponse';
import type { UpdateObligationPaymentRequest } from '../models/UpdateObligationPaymentRequest';
import type { CancelablePromise } from '../core/CancelablePromise';
import { OpenAPI } from '../core/OpenAPI';
import { request as __request } from '../core/request';
export class PaymentsService {
    /**
     * Create a single obligation payment
     * Creates a new obligation payment record in the database.
     * @param requestBody
     * @returns ObligationPaymentResponse Obligation payment created successfully
     * @throws ApiError
     */
    public static createObligationPayment(
        requestBody: CreateObligationPaymentRequest,
    ): CancelablePromise<ObligationPaymentResponse> {
        return __request(OpenAPI, {
            method: 'POST',
            url: '/admin/obligation-payment',
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Create a single obligation payment
     * Creates a new obligation payment record in the database.
     * @param requestBody
     * @returns ObligationPaymentResponse Obligation payment created successfully
     * @throws ApiError
     */
    public static updateObligationPayment(
        requestBody: UpdateObligationPaymentRequest,
    ): CancelablePromise<ObligationPaymentResponse> {
        return __request(OpenAPI, {
            method: 'PUT',
            url: '/admin/obligation-payment',
            body: requestBody,
            mediaType: 'application/json',
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Retrieve obligation payments for a specific student
     * Fetches a list of obligation payments made by or for a specific student.
     * @param studentId Unique identifier of the student
     * @returns ObligationPayment List of obligation payments retrieved successfully
     * @throws ApiError
     */
    public static getObligationPayments(
        studentId: number,
    ): CancelablePromise<Array<ObligationPayment>> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/obligation-payments/parent-payments/{student_id}',
            path: {
                'student_id': studentId,
            },
            errors: {
                400: `BAD REQUEST`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Get all obligation payments by obligation ID
     * Retrieves a list of all obligation payments associated with a specific obligation ID.
     * @param obligationId The ID of the obligation to retrieve payments for
     * @returns ObligationPayment A list of obligation payments
     * @throws ApiError
     */
    public static getParentPayments(
        obligationId: number,
    ): CancelablePromise<Array<ObligationPayment>> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/obligation-payments/{obligation_id}',
            path: {
                'obligation_id': obligationId,
            },
            errors: {
                400: `BAD REQUEST`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Retrieve parent payments by payment ID
     * Fetches payment details for a specific payment ID, accessible by ADMIN and TEACHER roles.
     * @param paymentId Unique identifier of the payment
     * @returns ObligationChildPayment Payment details retrieved successfully
     * @throws ApiError
     */
    public static getChildPaymentsByPaymentId(
        paymentId: number,
    ): CancelablePromise<Array<ObligationChildPayment>> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/obligation-payments/child-payments/{payment_id}',
            path: {
                'payment_id': paymentId,
            },
            errors: {
                400: `BAD REQUEST`,
                401: `UNAUTHORIZED`,
                403: `FORBIDDEN`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
    /**
     * Retrieve parent payments by payment ID
     * Fetches payment details for a specific payment ID, accessible by ADMIN and TEACHER roles.
     * @param parentPaymentId Unique identifier of the payment
     * @returns ObligationChildPayment Payment details retrieved successfully
     * @throws ApiError
     */
    public static getParentChildPaymentsByParentPaymentId(
        parentPaymentId: number,
    ): CancelablePromise<Array<ObligationChildPayment>> {
        return __request(OpenAPI, {
            method: 'GET',
            url: '/admin/obligation-payments/parent-child-payments/{parentPaymentId}',
            path: {
                'parentPaymentId': parentPaymentId,
            },
            errors: {
                400: `BAD REQUEST`,
                401: `UNAUTHORIZED`,
                403: `FORBIDDEN`,
                404: `NOT FOUND`,
                500: `INTERNAL SERVER ERROR`,
            },
        });
    }
}
