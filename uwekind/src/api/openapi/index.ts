/* generated using openapi-typescript-codegen -- do not edit */
/* istanbul ignore file */
/* tslint:disable */
/* eslint-disable */
export { ApiError } from './core/ApiError';
export { CancelablePromise, CancelError } from './core/CancelablePromise';
export { OpenAPI } from './core/OpenAPI';
export type { OpenAPIConfig } from './core/OpenAPI';

export { ActionKeyEnum } from './models/ActionKeyEnum';
export type { AddressCard } from './models/AddressCard';
export type { AddressCardRequest } from './models/AddressCardRequest';
export type { AddressCardUpdateRequest } from './models/AddressCardUpdateRequest';
export type { Admission } from './models/Admission';
export type { AdmissionMaterials } from './models/AdmissionMaterials';
export type { AdmissionMaterialsRequest } from './models/AdmissionMaterialsRequest';
export type { AdmissionRequest } from './models/AdmissionRequest';
export type { AdmissionsListResponse } from './models/AdmissionsListResponse';
export { AdmissionTypeEnum } from './models/AdmissionTypeEnum';
export type { AdmissionUpdateRequest } from './models/AdmissionUpdateRequest';
export type { Applicant } from './models/Applicant';
export type { ApplicantConversionRequest } from './models/ApplicantConversionRequest';
export type { ApplicantConversionResponse } from './models/ApplicantConversionResponse';
export type { ApplicantRequest } from './models/ApplicantRequest';
export type { ApplicantsStatusRequest } from './models/ApplicantsStatusRequest';
export type { ApplicantUpdateRequest } from './models/ApplicantUpdateRequest';
export { ApplicationTypeEnum } from './models/ApplicationTypeEnum';
export type { AssignStudentsRequest } from './models/AssignStudentsRequest';
export { CefrLevelEnum } from './models/CefrLevelEnum';
export type { ChildPayment } from './models/ChildPayment';
export type { CreateObligationPaymentRequest } from './models/CreateObligationPaymentRequest';
export type { CreateStudentObligationRequest } from './models/CreateStudentObligationRequest';
export { CurrencyEnum } from './models/CurrencyEnum';
export type { DeleteApplicantResponse } from './models/DeleteApplicantResponse';
export type { DeleteMultipleApplicantsRequest } from './models/DeleteMultipleApplicantsRequest';
export type { Document } from './models/Document';
export { DocumentTypeEnum } from './models/DocumentTypeEnum';
export type { ErrorResponse } from './models/ErrorResponse';
export type { Feedback } from './models/Feedback';
export type { FeedbackRequest } from './models/FeedbackRequest';
export { FeedbackTypeEnum } from './models/FeedbackTypeEnum';
export type { FeedbackUpdateRequest } from './models/FeedbackUpdateRequest';
export type { FeedbackWeightUpdateRequest } from './models/FeedbackWeightUpdateRequest';
export { GradeLevelClassEnum } from './models/GradeLevelClassEnum';
export { GradeLevelEnum } from './models/GradeLevelEnum';
export { IbProgramEnum } from './models/IbProgramEnum';
export type { ObligationChildPayment } from './models/ObligationChildPayment';
export type { ObligationDetails } from './models/ObligationDetails';
export type { ObligationPayment } from './models/ObligationPayment';
export type { ObligationPaymentResponse } from './models/ObligationPaymentResponse';
export type { ObligationsResponse } from './models/ObligationsResponse';
export type { ObligationSubTypesResponse } from './models/ObligationSubTypesResponse';
export { ObligationTypeEnum } from './models/ObligationTypeEnum';
export type { PaginatedApplicantsResponse } from './models/PaginatedApplicantsResponse';
export type { Payment } from './models/Payment';
export type { PaymentsResponse } from './models/PaymentsResponse';
export { PaymentTypeEnum } from './models/PaymentTypeEnum';
export { StatusEnum } from './models/StatusEnum';
export type { Student } from './models/Student';
export type { StudentObligation } from './models/StudentObligation';
export type { StudentResponse } from './models/StudentResponse';
export type { StudentStatusUpdateRequest } from './models/StudentStatusUpdateRequest';
export type { StudentStatusUpdateResponse } from './models/StudentStatusUpdateResponse';
export type { StudentTaxStatusRequest } from './models/StudentTaxStatusRequest';
export { StudentTypeEnum } from './models/StudentTypeEnum';
export type { Subject } from './models/Subject';
export type { SubjectRequest } from './models/SubjectRequest';
export type { SystemAcademicYear } from './models/SystemAcademicYear';
export type { TableResponse } from './models/TableResponse';
export type { TeacherComment } from './models/TeacherComment';
export type { TeacherCommentRequest } from './models/TeacherCommentRequest';
export type { UpdateInsuranceMonthRequest } from './models/UpdateInsuranceMonthRequest';
export type { UpdateObligationPaymentRequest } from './models/UpdateObligationPaymentRequest';
export type { UpdateStudentStatusRequest } from './models/UpdateStudentStatusRequest';

export { AdminService } from './services/AdminService';
export { AdminAddressCardService } from './services/AdminAddressCardService';
export { AdminAdmissionMaterialsService } from './services/AdminAdmissionMaterialsService';
export { AdminAdmissionsService } from './services/AdminAdmissionsService';
export { AdminDocumentsService } from './services/AdminDocumentsService';
export { AdminFeedbackService } from './services/AdminFeedbackService';
export { AdminObligationSubTypesService } from './services/AdminObligationSubTypesService';
export { AdminStudentsService } from './services/AdminStudentsService';
export { AdminSubjectService } from './services/AdminSubjectService';
export { AdminSystemService } from './services/AdminSystemService';
export { ApplicantsService } from './services/ApplicantsService';
export { FinancesService } from './services/FinancesService';
export { InformationService } from './services/InformationService';
export { PaymentsService } from './services/PaymentsService';
export { StudentService } from './services/StudentService';
export { StudentObligationsService } from './services/StudentObligationsService';
export { SystemService } from './services/SystemService';
export { TeacherCommentsService } from './services/TeacherCommentsService';
