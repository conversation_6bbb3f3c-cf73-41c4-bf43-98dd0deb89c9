import { Navigate, Route, Routes } from "react-router-dom";

import { ROUTES } from "@constants/routes";
import ProtectedRoute from "@guards/ProtectedRoute";
import { useNavigationTabs } from "@hooks/useNavigationTabs";
import Students from "@pages/Students";
import Unavailable from "@pages/Unavailable";

import "./App.css";

function App() {
  const tabs = useNavigationTabs();

  return (
    <Routes>
      <Route element={<ProtectedRoute />}>
        {tabs.map(
          (tab) =>
            !tab.isAvailable && (
              <Route
                path={tab.route}
                element={<Unavailable page={tab.label} />}
              />
            )
        )}

        <Route path={ROUTES.STUDENTS} element={<Students />} />
        <Route
          path={ROUTES.STUDENT_DETAILS.APPLICATION()}
          element={<Unavailable page="Кандидатура" />}
        />

        <Route path={ROUTES.ROOT} element={<Navigate to={ROUTES.STUDENTS} />} />
      </Route>
    </Routes>
  );
}

export default App;
