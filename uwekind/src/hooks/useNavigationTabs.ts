import { useTranslation } from "react-i18next";
import { ROUTES } from "@constants/routes";

export const useNavigationTabs = () => {
  const { t: translate } = useTranslation();

  return [
    {
      label: translate("navBtn.newAdmission"),
      route: ROUTES.NEW_ADMISSION,
    },
    {
      label: translate("navBtn.inProgress"),
      route: ROUTES.PROGRESS,
    },
    {
      label: translate("navBtn.students"),
      route: ROUTES.STUDENTS,
      isAvailable: true,
    },
    {
      label: translate("navBtn.entrySessions"),
      route: ROUTES.ENTRY_SESSIONS,
    },
    {
      label: translate("navBtn.financeReport"),
      route: ROUTES.FINANCE_REPORT,
    },
  ];
};
