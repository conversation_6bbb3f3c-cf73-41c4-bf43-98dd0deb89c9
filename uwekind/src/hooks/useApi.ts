import { useCallback } from "react";
import { useNavigate } from "react-router-dom";

import { interceptRequest as baseInterceptor } from "@interceptors/api.interceptor";
import { useAuth } from "./useAuth";

export function useApi() {
  const { getAccessToken, onLogout } = useAuth();
  const navigate = useNavigate();

  const interceptRequest = useCallback(
    async <Type>(
      serviceFunc: (...args: any[]) => Promise<Type>,
      options: { headers?: Record<string, string> } = {},
      ...args: any[]
    ): Promise<Type> => {
      return await baseInterceptor(
        serviceFunc,
        {
          ...options,
          getAccessToken,
          onLogout,
          navigate,
        },
        ...args
      );
    },
    [getAccessToken, onLogout, navigate]
  );

  return { interceptRequest };
}
