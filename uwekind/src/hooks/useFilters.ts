import { useTranslation } from "react-i18next";
import type { GetStudentsArgs } from "@utils/buildGetStudentsArgs";

interface FilterField {
  id: keyof GetStudentsArgs;
  label: string;
  column?: number;
  sx?: string;
  isSelect?: boolean;
}

export const useFilters = (): FilterField[] => {
  const { t: translate } = useTranslation();

  return [
    {
      id: "fullName",
      label: translate("filter.studentNameInput"),
      column: 1,
    },
    {
      id: "currentGrade",
      label: translate("filter.gradeInput"),
      sx: "15.625rem",
      isSelect: true,
    },
    {
      id: "gradeLevelClass",
      label: translate("filter.classInput"),
      sx: "8.375rem",
      isSelect: true,
    },
    {
      id: "identityNumber",
      label: translate("filter.pinInput"),
      column: 3,
    },
    {
      id: "motherName",
      label: translate("filter.motherNameInput"),
      column: 1,
    },
    {
      id: "motherEmail",
      label: translate("filter.motherEmailInput"),
      column: 2,
    },
    {
      id: "motherPhone",
      label: translate("filter.motherPhoneInput"),
      column: 3,
    },
    {
      id: "fatherName",
      label: translate("filter.fatherNameInput"),
      column: 1,
    },
    {
      id: "fatherEmail",
      label: translate("filter.fatherEmailInput"),
      column: 2,
    },
    {
      id: "fatherPhone",
      label: translate("filter.fatherPhoneInput"),
      column: 3,
    },
  ];
};
