import { useContext } from "react";

import { ERRORS } from "@constants/general";
import {
    AdminStudentsContext,
    type AdminStudentsContextType,
} from "@contexts/AdminStudentsContext";

export const useStudents = (): AdminStudentsContextType => {
  const context = useContext(AdminStudentsContext);
  if (!context) {
    throw new Error(
      ERRORS.CONTEXT_ERROR_MESSAGE("useStudents", "StudentsProvider")
    );
  }
  return context;
};
