import { useTranslation } from "react-i18next";
import type { GridColDef } from "@mui/x-data-grid";

export const useTable = (rows: any): GridColDef<(typeof rows)[number]>[] => {
  const { t: translate } = useTranslation();

  const columnWidth = 203.11;
  const renderWithFallback = (field: string) => ({
    field,
    renderCell: (params: any) =>
      params.value?.toString().trim() ? params.value : "-",
  });

  return [
    {
      ...renderWithFallback("full_name"),
      headerName: translate("tableColumns.studentName"),
      width: columnWidth,
    },
    {
      ...renderWithFallback("identity_number"),
      headerName: translate("tableColumns.pin"),
      width: columnWidth,
    },
    {
      ...renderWithFallback("current_grade"),
      headerName: translate("tableColumns.grade"),
      width: columnWidth,
    },
    {
      ...renderWithFallback("mother_name"),
      headerName: translate("tableColumns.motherName"),
      width: columnWidth,
    },
    {
      ...renderWithFallback("mother_email"),
      headerName: translate("tableColumns.motherEmail"),
      width: columnWidth,
    },
    {
      ...renderWithFallback("mother_phone"),
      headerName: translate("tableColumns.motherPhone"),
      width: columnWidth,
    },
    {
      ...renderWithFallback("father_name"),
      headerName: translate("tableColumns.fatherName"),
      width: columnWidth,
    },
    {
      ...renderWithFallback("father_email"),
      headerName: translate("tableColumns.fatherEmail"),
      width: columnWidth,
    },
    {
      ...renderWithFallback("father_phone"),
      headerName: translate("tableColumns.fatherPhone"),
      width: columnWidth,
    },
  ];
};
