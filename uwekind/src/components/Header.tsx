import { useEffect, useState } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

import LogoutIcon from "@mui/icons-material/Logout";
import AppBar from "@mui/material/AppBar";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import { useTheme } from "@mui/material/styles";
import Toolbar from "@mui/material/Toolbar";
import Typography from "@mui/material/Typography";
import useMediaQuery from "@mui/material/useMediaQuery";

import NavigationTabs from "@components/NavigationTabs";
import { ROUTES } from "@constants/routes";
import { useApi } from "@hooks/useApi";
import { useAuth } from "@hooks/useAuth";
import { AdminSystemService, type SystemAcademicYear } from "@openapi/index";

import logo from "@assets/uwekind-logo.svg";
import { headerStyles, sharedStyles } from "@styles/index";

export default function Header() {
  const { t: translation } = useTranslation();
  const navigate = useNavigate();
  const { interceptRequest } = useApi();
  const theme = useTheme();
  const isTabletView = useMediaQuery(theme.breakpoints.down("md"));

  const { getCurrentSystemAcademicYear } = AdminSystemService;
  const { username, onLogout } = useAuth();

  const [year, setYear] = useState<SystemAcademicYear | null>(null);

  const handleLogoClick = () => {
    window.scrollTo({ top: 0, behavior: "smooth" });
    navigate(ROUTES.STUDENTS);
  };

  useEffect(() => {
    const fetchCurrentYear = async () => {
      try {
        const year = await interceptRequest(
          getCurrentSystemAcademicYear,
          {},
          {}
        );
        setYear(year);
      } catch (error) {
        console.error((error as Error).message);
      }
    };

    fetchCurrentYear();
  }, [interceptRequest]);

  return (
    <AppBar position="sticky" elevation={0} sx={headerStyles.header}>
      <Toolbar sx={headerStyles.headerToolbar}>
        <Box
          component="div"
          onClick={handleLogoClick}
          sx={headerStyles.headerBox}>
          <Box
            component="img"
            src={logo}
            alt="Uwekind logo"
            sx={headerStyles.logo}
          />
          <Typography component="h1" sx={headerStyles.title}>
            {translation("headerTitle")}
          </Typography>
        </Box>
        <Box component="div" sx={headerStyles.headerBox}>
          <Button
            variant={sharedStyles.filledVariant}
            color="secondary"
            sx={headerStyles.changeYearButton}>
            {isTabletView
              ? year?.current_system_academic_year
                ? year.current_system_academic_year
                : ""
              : translation("changeYearButton")}
          </Button>
          {!isTabletView && (
            <Typography component="h2" sx={headerStyles.yearText}>
              {year?.current_system_academic_year
                ? `${translation("yearLabel")} ${
                    year.current_system_academic_year
                  }`
                : ""}
            </Typography>
          )}
          <Typography component="h2" sx={headerStyles.username}>
            {username}
          </Typography>
          <IconButton onClick={onLogout} color="inherit">
            <LogoutIcon />
          </IconButton>
        </Box>
      </Toolbar>
      <NavigationTabs />
    </AppBar>
  );
}
