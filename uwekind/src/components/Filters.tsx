import type { ChangeEvent, Dispatch, SetStateAction } from "react";
import { useTranslation } from "react-i18next";

import SearchIcon from "@mui/icons-material/Search";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import FormControl from "@mui/material/FormControl";
import Grid from "@mui/material/Grid";
import InputAdornment from "@mui/material/InputAdornment";
import InputLabel from "@mui/material/InputLabel";
import MenuItem from "@mui/material/MenuItem";
import Select, { type SelectChangeEvent } from "@mui/material/Select";
import TextField from "@mui/material/TextField";

import {
  GRADE_SELECTOR_FILTER,
  TABLE_DEFAULT_OFFSET,
  TABLE_PAGE_SIZE,
} from "@constants/general";
import { useFilters } from "@hooks/useFilters";
import { useStudents } from "@hooks/useStudents";
import { GradeLevelClassEnum, GradeLevelEnum } from "@openapi/index";
import type { GetStudentsArgs } from "@utils/buildGetStudentsArgs";

import { filtersStyles, sharedStyles } from "@styles/index";

interface FiltersProps {
  setVisibility: (value: boolean) => void;
  filterValues: Partial<GetStudentsArgs>;
  setFilterValues: Dispatch<SetStateAction<Partial<GetStudentsArgs>>>;
}

export default function Filters({
  setVisibility,
  filterValues,
  setFilterValues,
}: FiltersProps) {
  const { t: translate } = useTranslation();
  const filters = useFilters();
  const { setFilters } = useStudents();

  const gradeLevelOptions = Object.entries(GradeLevelEnum).map(
    ([key, value]) => ({
      key: key as keyof typeof GradeLevelEnum,
      label: value,
    })
  );

  const gradeLevelClassOptions = Object.entries(GradeLevelClassEnum).map(
    ([key, value]) => ({
      key: key as keyof typeof GradeLevelClassEnum,
      label: value,
    })
  );

  const onFilterChange =
    (filterId: keyof GetStudentsArgs) =>
    (
      event:
        | SelectChangeEvent<string | number | boolean | string[]>
        | ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
    ) => {
      setFilterValues((prev) => ({
        ...prev,
        [filterId]: event.target.value,
      }));
    };

  function onApplyFilters() {
    setFilters((prev) => ({
      ...prev,
      ...filterValues,
      offset: TABLE_DEFAULT_OFFSET,
    }));

    setVisibility(false);
  }

  function onClearFilters() {
    setFilterValues({});

    setFilters({
      limit: TABLE_PAGE_SIZE,
      offset: TABLE_DEFAULT_OFFSET,
    });

    setVisibility(false);
  }

  return (
    <Box component="div" sx={sharedStyles.componentWrapper}>
      <Grid container spacing={2}>
        <Grid container spacing={2} sx={filtersStyles.container}>
          {filters.map((filter) => (
            <Grid key={filter.id} sx={filtersStyles.getFilterGridSx(filter)}>
              {filter.isSelect ? (
                <FormControl fullWidth size="small">
                  <InputLabel id={filter.id}>{filter.label}</InputLabel>
                  <Select
                    labelId={filter.id}
                    value={filterValues[filter.id] || ""}
                    label={filter.label}
                    onChange={onFilterChange(filter.id)}>
                    {(filter.id === GRADE_SELECTOR_FILTER
                      ? gradeLevelOptions
                      : gradeLevelClassOptions
                    ).map((option) => (
                      <MenuItem key={option.key} value={option.label}>
                        {option.label}
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              ) : (
                <TextField
                  id={filter.id}
                  label={filter.label}
                  type="search"
                  variant={sharedStyles.outlinedVariant}
                  size="small"
                  fullWidth
                  value={filterValues[filter.id] || ""}
                  onChange={onFilterChange(filter.id)}
                  slotProps={{
                    input: {
                      endAdornment: (
                        <InputAdornment position="end">
                          <SearchIcon />
                        </InputAdornment>
                      ),
                    },
                  }}
                />
              )}
            </Grid>
          ))}
        </Grid>
        <Grid size={12} sx={filtersStyles.btns}>
          <Button
            onClick={onApplyFilters}
            sx={sharedStyles.btn}
            variant={sharedStyles.filledVariant}>
            {translate("filter.applyBtn")}
          </Button>
          <Button
            onClick={onClearFilters}
            sx={sharedStyles.btn}
            variant={sharedStyles.filledVariant}>
            {translate("filter.clearBtn")}
          </Button>
        </Grid>
      </Grid>
    </Box>
  );
}
