import type { SyntheticEvent } from "react";
import { useLocation, useNavigate } from "react-router-dom";

import Box from "@mui/material/Box";
import Tab from "@mui/material/Tab";
import Tabs from "@mui/material/Tabs";

import { useNavigationTabs } from "@hooks/useNavigationTabs";

import { navigationStyles } from "@styles/navigation";

export default function NavigationTabs() {
  const tabs = useNavigationTabs();
  const location = useLocation();
  const navigate = useNavigate();

  const currentTabIndex = tabs.findIndex((tab) =>
    location.pathname.startsWith(tab.route)
  );

  const handleChange = (_: SyntheticEvent, newValue: number) => {
    navigate(tabs[newValue].route);
  };

  return (
    <Box component="div" sx={navigationStyles.nav}>
      <Tabs
        value={currentTabIndex}
        onChange={handleChange}
        aria-label="nav tabs"
        role="navigation"
        sx={navigationStyles.navTabs}>
        {tabs.map(({ label }) => (
          <Tab key={label} label={label} sx={navigationStyles.navTabBtn} />
        ))}
      </Tabs>
    </Box>
  );
}
