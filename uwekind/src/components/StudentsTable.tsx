import { useEffect } from "react";
import { useTranslation } from "react-i18next";
import { useNavigate } from "react-router-dom";

import Box from "@mui/material/Box";
import { DataGrid } from "@mui/x-data-grid";

import { TABLE_PAGE_SIZE, TABLE_ROW_HEIGHT } from "@constants/general";
import { ROUTES } from "@constants/routes";
import { useStudents } from "@hooks/useStudents";
import { useTable } from "@hooks/useTable";

import { tableStyles } from "@styles/table";

export default function StudentsTable() {
  const { t: translate } = useTranslation();
  const navigate = useNavigate();

  const { students, studentsTotal, fetchStudents, page, setPage, setFilters } =
    useStudents();
  const columns = useTable(students);

  useEffect(() => {
    setFilters((prev) => {
      if (prev === undefined) {
        return {
          limit: TABLE_PAGE_SIZE,
          offset: page * TABLE_PAGE_SIZE,
        };
      }

      return {
        ...prev,
        offset: page * TABLE_PAGE_SIZE,
      };
    });
  }, [page]);

  useEffect(() => {
    fetchStudents();
  }, [fetchStudents]);

  return (
    <Box sx={tableStyles.container}>
      <DataGrid
        rows={students}
        columns={columns}
        getRowId={(row) => row.student_id}
        rowHeight={TABLE_ROW_HEIGHT}
        columnHeaderHeight={TABLE_ROW_HEIGHT}
        paginationMode="server"
        rowCount={studentsTotal}
        pageSizeOptions={[TABLE_PAGE_SIZE]}
        initialState={{
          pagination: {
            paginationModel: {
              pageSize: TABLE_PAGE_SIZE,
              page,
            },
          },
        }}
        onPaginationModelChange={({ page }) => {
          setPage(page);
          localStorage.setItem("studentsTablePage", String(page));
        }}
        localeText={{
          paginationDisplayedRows: ({ from, to, count }) =>
            `${from}–${to} ${translate("paginationLabel")} ${count}`,
        }}
        checkboxSelection
        disableRowSelectionOnClick
        disableColumnMenu
        isRowSelectable={(params) => {
          const student = params.row;
          return (
            student.is_student &&
            student.applied_for_academic_year !== student.academic_year
          );
        }}
        onRowClick={(params) => {
          navigate(ROUTES.STUDENT_DETAILS.APPLICATION(params.row.student_id));
        }}
        sx={tableStyles.table}
      />
    </Box>
  );
}
