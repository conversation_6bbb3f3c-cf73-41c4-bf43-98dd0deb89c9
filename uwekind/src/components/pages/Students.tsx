import { useState } from "react";
import { useTranslation } from "react-i18next";

import FilterListIcon from "@mui/icons-material/FilterList";
import Box from "@mui/material/Box";
import Button from "@mui/material/Button";
import InputAdornment from "@mui/material/InputAdornment";

import Filters from "@components/Filters";
import StudentsTable from "@components/StudentsTable";
import type { GetStudentsArgs } from "@utils/buildGetStudentsArgs";

import { sharedStyles, studentsStyles } from "@styles/index";

export default function Students() {
  const { t: translate } = useTranslation();
  const [areFiltersVisible, setAreFiltersVisible] = useState(false);
  const [filterValues, setFilterValues] = useState<Partial<GetStudentsArgs>>(
    {}
  );

  const toggleFilters = (visibility: boolean) => {
    setAreFiltersVisible(!visibility);
  };

  return (
    <Box component="div" sx={studentsStyles.pageWrapper}>
      <Box component="div" sx={studentsStyles.filterBtnWrapper}>
        <Button
          variant={sharedStyles.outlinedVariant}
          startIcon={
            <InputAdornment position="start" sx={studentsStyles.filterBtnIcon}>
              <FilterListIcon color="primary" />
            </InputAdornment>
          }
          onClick={() => toggleFilters(areFiltersVisible)}
          sx={[sharedStyles.btn, studentsStyles.filterBtn]}>
          {translate("filter.showBtn")}
        </Button>
      </Box>
      {areFiltersVisible && (
        <Filters
          setVisibility={setAreFiltersVisible}
          filterValues={filterValues}
          setFilterValues={setFilterValues}
        />
      )}
      <Box
        component="div"
        sx={[sharedStyles.componentWrapper, studentsStyles.studentsManageBtns]}>
        <Button sx={sharedStyles.btn} variant={sharedStyles.filledVariant}>
          {translate("inviteBtn")}
        </Button>
        <Button sx={sharedStyles.btn} variant={sharedStyles.filledVariant}>
          {translate("cancelBtn")}
        </Button>
      </Box>
      <StudentsTable />
    </Box>
  );
}
