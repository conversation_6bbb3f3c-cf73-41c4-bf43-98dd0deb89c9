import { useTranslation } from "react-i18next";
import { useParams } from "react-router-dom";

import ReportIcon from "@mui/icons-material/Report";
import Box from "@mui/material/Box";
import Typography from "@mui/material/Typography";

import { unavailableStyles } from "@styles/unavailablePage";

type UnavailableProps = {
  page: string;
};

export default function Unavailable({ page }: UnavailableProps) {
  const { t: translate } = useTranslation();
  const { id: studentId } = useParams();

  return (
    <Box component="div" sx={unavailableStyles.pageWrapper}>
      <ReportIcon color="warning" sx={unavailableStyles.icon} />
      <Typography component="h1" color="warning" sx={unavailableStyles.title}>
        {studentId
          ? `${translate("unavailablePageWarning1")} "${page}" ${translate(
              "unavailablePageWarning3"
            )}${studentId}.`
          : `${translate("unavailablePageWarning1")} "${page}" ${translate(
              "unavailablePageWarning2"
            )}`}
      </Typography>
    </Box>
  );
}
