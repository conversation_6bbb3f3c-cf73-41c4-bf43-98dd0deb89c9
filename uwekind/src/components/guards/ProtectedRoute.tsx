import { useEffect } from "react";
import { Outlet } from "react-router-dom";

import Header from "@components/Header";
import { useAuth } from "@hooks/useAuth";

const ProtectedRoute = () => {
  const { isAuthenticated, onLogin } = useAuth();

  useEffect(() => {
    const checkAuth = async () => {
      if (!isAuthenticated) {
        await onLogin();
      }
    };

    checkAuth();
  }, [isAuthenticated, onLogin]);

  if (isAuthenticated)
    return (
      <>
        <Header />
        <Outlet />
      </>
    );

  return null;
};

export default ProtectedRoute;
