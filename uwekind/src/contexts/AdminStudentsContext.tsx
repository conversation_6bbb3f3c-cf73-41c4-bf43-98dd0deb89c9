import {
  createContext,
  useCallback,
  useMemo,
  useState,
  type FC,
  type ReactNode,
} from "react";

import {
  TABLE_DEFAULT_OFFSET,
  TABLE_DEFAULT_STUDENTS_TOTAL,
} from "@constants/general";
import { useApi } from "@hooks/useApi";
import { AdminStudentsService, type Student } from "@openapi/index";
import {
  buildGetStudentsArgs,
  type GetStudentsArgs,
} from "@utils/buildGetStudentsArgs";

export interface AdminStudentsContextType {
  students: Student[];
  studentsTotal: number;
  fetchStudents: () => Promise<void>;
  page: number;
  setPage: (value: number) => void;
  filters: GetStudentsArgs | undefined;
  setFilters: (
    f:
      | GetStudentsArgs
      | undefined
      | ((prev: GetStudentsArgs | undefined) => GetStudentsArgs | undefined)
  ) => void;
}

export const AdminStudentsContext = createContext<
  AdminStudentsContextType | undefined
>(undefined);

export const StudentsProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const { interceptRequest } = useApi();
  const { getStudents } = AdminStudentsService;

  const [students, setStudents] = useState<Student[]>([]);
  const [studentsTotal, setStudentsTotal] = useState<number>(
    TABLE_DEFAULT_STUDENTS_TOTAL
  );

  const [page, setPage] = useState(() => {
    const saved = localStorage.getItem("studentsTablePage");
    return saved ? parseInt(saved) : TABLE_DEFAULT_OFFSET;
  });

  const [filters, setFilters] = useState<GetStudentsArgs | undefined>(
    undefined
  );

  const fetchStudents = useCallback(async () => {
    if (!filters) return;

    try {
      const args = buildGetStudentsArgs(filters);
      const result = await interceptRequest(getStudents, {}, ...args);
      setStudents(result.students ?? []);
      setStudentsTotal(result.totalCount ?? 0);
    } catch (error) {
      console.error((error as Error).message);
    }
  }, [filters, interceptRequest]);

  const contextValue = useMemo(
    () => ({
      students,
      studentsTotal,
      fetchStudents,
      page,
      setPage,
      filters,
      setFilters,
    }),
    [students, studentsTotal, fetchStudents, page, setPage, filters, setFilters]
  );

  return (
    <AdminStudentsContext.Provider value={contextValue}>
      {children}
    </AdminStudentsContext.Provider>
  );
};
