import {
  createContext,
  useCallback,
  useMemo,
  useState,
  type FC,
  type ReactNode,
} from "react";

import { extractAccessToken, extractUsername } from "@utils/auth";
import { authService } from "@utils/virava";

export interface AuthContextType {
  isAuthenticated: boolean;
  username: string | null;
  setUsername: (value: string | null) => void;
  onLogin: () => Promise<void>;
  onLogout: () => Promise<void>;
  getAccessToken: () => string | null;
}

export const AuthContext = createContext<AuthContextType | null>(null);

export const AuthProvider: FC<{ children: ReactNode }> = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(
    authService.isAuthenticated()
  );
  const [username, setUsername] = useState<string | null>(null);

  const onLogin = useCallback(async () => {
    await authService.login();
    setIsAuthenticated(true);
    setUsername(extractUsername());
  }, [setIsAuthenticated, setUsername]);

  const onLogout = useCallback(async () => {
    await authService.logout(window.location.origin);
    setIsAuthenticated(false);
    setUsername(null);
  }, [setIsAuthenticated, setUsername]);

  const getAccessToken = () => {
    return extractAccessToken();
  };

  const contextValue = useMemo(
    () => ({
      isAuthenticated,
      onLogin,
      onLogout,
      getAccessToken,
      username,
      setUsername,
    }),
    [isAuthenticated, username]
  );

  return (
    <AuthContext.Provider value={contextValue}>{children}</AuthContext.Provider>
  );
};
