{"apiTest": "Текуща учебна година", "cancelBtn": "Откажи", "changeYearButton": "Смени", "filter": {"applyBtn": "Прило<PERSON>и", "classInput": "Паралелка", "clearBtn": "Изчисти", "fatherEmailInput": "Имейл на баща", "fatherNameInput": "Име на баща", "fatherPhoneInput": "Телефон на баща", "gradeInput": "<PERSON><PERSON><PERSON><PERSON>", "motherEmailInput": "Имейл на майка", "motherNameInput": "Име на майка", "motherPhoneInput": "Телефон на майка", "pinInput": "Търси по ЕГН", "showBtn": "Филтри", "studentNameInput": "Име"}, "headerTitle": "Управление прием на ученици", "inviteBtn": "Покани отново", "logout": "Изход", "navBtn": {"entrySessions": "Приемни сесии", "financeReport": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> отчет", "inProgress": "В процес", "newAdmission": "Нов прием", "students": "Ученици"}, "paginationLabel": "от общо", "ratingMessage": "Днес си", "tableColumns": {"fatherEmail": "Имейл на баща", "fatherName": "Баща", "fatherPhone": "Телефон на баща", "grade": "<PERSON><PERSON><PERSON><PERSON>", "motherEmail": "Имейл на майка", "motherName": "Майка", "motherPhone": "Телефон на майка", "pin": "ЕГН", "studentName": "Име"}, "unavailablePageWarning1": "Страницата", "unavailablePageWarning2": "в момента не е налична.", "unavailablePageWarning3": "в момента не е налична за ученик №", "yearLabel": "Учебна година:"}