{"apiTest": "Current academic year", "cancelBtn": "Cancel", "changeYearButton": "Change", "filter": {"applyBtn": "Apply", "classInput": "Class", "clearBtn": "Clear", "fatherEmailInput": "Email of father", "fatherNameInput": "Name of father", "fatherPhoneInput": "Telephone of father", "gradeInput": "Grade", "motherEmailInput": "Email of mother", "motherNameInput": "Name of mother", "motherPhoneInput": "Telephone of mother", "pinInput": "Search by personal identification number", "showBtn": "Filters", "studentNameInput": "Name"}, "headerTitle": "Student admissions management", "inviteBtn": "Invite again", "logout": "Logout", "navBtn": {"entrySessions": "Entry sessions", "financeReport": "Financial report", "inProgress": "In progress", "newAdmission": "New admission", "students": "Students"}, "paginationLabel": "of", "ratingMessage": "Today you are", "tableColumns": {"fatherEmail": "Email of father", "fatherName": "Father", "fatherPhone": "Telephone of father", "grade": "Grade", "motherEmail": "Email of mother", "motherName": "Mother", "motherPhone": "Telephone of mother", "pin": "PIN", "studentName": "Name"}, "unavailablePageWarning1": "Page", "unavailablePageWarning2": "is currently not available.", "unavailablePageWarning3": "is currently not available for student with id No. ", "yearLabel": "Academic year:"}