import { jwtDecode, type JwtPayload } from "jwt-decode";
import { ACCESS_TOKEN } from "@constants/api";

interface CustomJwtPayload extends JwtPayload {
  preferred_username?: string;
  email?: string;
  roles?: string[];
}

export function extractAccessToken() {
  return localStorage.getItem(ACCESS_TOKEN);
}

export function decodeAccessToken() {
  const token = extractAccessToken();
  if (!token) return null;

  try {
    return jwtDecode<CustomJwtPayload>(token);
  } catch (error) {
    console.error("Invalid token:", error);
    return null;
  }
}

export function extractUsername() {
  const payload = decodeAccessToken();
  if (!payload) return null;

  try {
    return payload.preferred_username ?? payload.email ?? null;
  } catch (error) {
    console.error("", error);
    return null;
  }
}
