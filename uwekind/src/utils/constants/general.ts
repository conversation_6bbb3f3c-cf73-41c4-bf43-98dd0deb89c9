// Error Messages
export const ERRORS = {
  CONTEXT_ERROR_MESSAGE: (hookName: string, providerName: string) =>
    `${hookName} must be used within ${providerName}`,
};

// Table Configuration
export const TABLE_ROW_HEIGHT = 40;
export const TABLE_PAGE_SIZE = 30;
export const TABLE_DEFAULT_OFFSET = 0;
export const TABLE_DEFAULT_STUDENTS_TOTAL = 0;

// Filters Configuration
export const GRADE_SELECTOR_FILTER = "currentGrade";
