import type {
  AdminStudentsService,
  GradeLevelClassEnum,
  GradeLevelEnum,
  StatusEnum,
} from "@openapi/index";

export interface GetStudentsArgs {
  fullName?: string;
  appliedForGrade?: Array<GradeLevelEnum>;
  currentGrade?: Array<GradeLevelEnum>;
  gradeLevelClass?: Array<GradeLevelClassEnum>;
  identityNumber?: string;
  academicYear?: string;
  appliedForAcademicYear?: Array<string>;
  status?: Array<StatusEnum>;
  motherName?: string;
  motherEmail?: string;
  motherPhone?: string;
  fatherName?: string;
  fatherEmail?: string;
  fatherPhone?: string;
  insuranceMonth?: number;
  isStudent?: boolean;
  inProcess?: boolean;
  limit?: number;
  offset?: number;
}

type GetStudentsArgsTuple = Parameters<typeof AdminStudentsService.getStudents>;

export const buildGetStudentsArgs = (
  filters: GetStudentsArgs
): GetStudentsArgsTuple => {
  return [
    filters.fullName,
    filters.appliedForGrade,
    filters.currentGrade,
    filters.gradeLevelClass,
    filters.identityNumber,
    filters.academicYear,
    filters.appliedForAcademicYear,
    filters.status,
    filters.motherName,
    filters.motherEmail,
    filters.motherPhone,
    filters.fatherName,
    filters.fatherEmail,
    filters.fatherPhone,
    filters.insuranceMonth,
    filters.isStudent,
    filters.inProcess,
    filters.limit,
    filters.offset,
  ];
};
