export const headerStyles = {
  header: { minHeight: "6.063rem" },
  headerToolbar: {
    justifyContent: "space-between",
    minHeight: "3.438rem !important",
  },
  headerBox: {
    display: "flex",
    alignItems: "center",
  },
  logo: {
    width: "100%",
    height: "auto",
    maxWidth: "2rem",
    cursor: "pointer",
  },
  title: {
    ml: 1,
    fontSize: "1rem",
    fontWeight: 700,
    cursor: "pointer",
  },
  changeYearButton: {
    mr: { md: 2, sm: 3 },
    px: 1,
    fontWeight: 700,
    letterSpacing: "0.025rem",
    lineHeight: "1.5rem",
    textTransform: "capitalize",
  },
  yearText: { fontSize: "0.875rem", fontWeight: 600 },
  username: {
    ml: { xl: 14, md: 5 },
    mr: { xl: 3, md: 1 },
    fontSize: "0.875rem",
    fontWeight: 600,
    textTransform: "capitalize",
  },
};
