export const studentsStyles = {
  pageWrapper: {
    display: "flex",
    flexDirection: "column",
    gap: "1rem",
    width: "100%",
    height: "100%",
    maxHeight: "calc(100vh - 6.063rem)",
    p: "1rem",
  },
  filterBtnWrapper: {
    display: "flex",
    justifyContent: "right",
    paddingTop: "0.375rem",
  },
  filterBtn: {
    p: 1,
    fontWeight: 600,
  },
  filterBtnIcon: { margin: 0 },
  studentsManageBtns: {
    display: "flex",
    gap: "1rem",
    alignItems: "left",
  },
};
