export const tableStyles = {
  container: { width: "100%", height: "100%", flexGrow: 1, minHeight: 0 },
  table: {
    "& .MuiDataGrid-columnHeaders": {
      backgroundColor: "background.paper",
      borderBottom: 1,
      borderColor: "grey.300",
    },
    "& .MuiDataGrid-columnHeader": {
      backgroundColor: "background.paper",
      border: "none !important",
    },
    "& .MuiDataGrid-columnHeaderTitle": {
      fontSize: "0.75rem",
      fontWeight: 600,
    },
    "& .MuiDataGrid-columnSeparator": {
      display: "none",
    },
    "& .MuiDataGrid-columnHeaderCheckbox, & .MuiDataGrid-cellCheckbox": {
      maxWidth: "2.625rem !important",
    },
    "& .MuiDataGrid-row": {
      borderBottom: 1,
      borderColor: "grey.300",
    },
    "& .MuiDataGrid-row.Mui-selected": {
      backgroundColor: "inherit !important",
    },
    "& .MuiDataGrid-row:hover": {
      cursor: "pointer",
    },
    "& .MuiDataGrid-cell": {
      px: "0.75rem",
      fontSize: "0.75rem",
      fontWeight: 400,
      border: "none",
    },
    "& .MuiDataGrid-cellEmpty": {
      display: "none",
    },
    "& .MuiDataGrid-scrollbar": {
      marginTop: "1px",
    },
    "& .MuiDataGrid-filler, & .MuiDataGrid-scrollbarFiller": {
      backgroundColor: "inherit !important",
      border: "none !important",
    },
    "& .MuiDataGrid-footerContainer": {
      height: "100%",
      maxHeight: "4rem",
    },
    "& .MuiTablePagination-actions": {
      m: 0,
    },
    "& .MuiTablePagination-selectLabel, & .MuiTablePagination-displayedRows": {
      fontSize: "0.75rem",
      fontWeight: 400,
    },
    "& .MuiDataGrid-selectedRowCount": {
      display: "none",
    },
  },
};
