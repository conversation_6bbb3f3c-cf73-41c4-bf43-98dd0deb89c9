export const filtersStyles = {
  container: { maxWidth: "72.313rem" },
  btns: {
    display: "flex",
    gap: "1rem",
    alignItems: "center",
    justifyContent: "center",
    py: 1,
  },
  getFilterGridSx: (field: { sx?: string; column?: number }) => {
    const baseSmallWidth = "29.438rem";

    const smallView = {
      xs: baseSmallWidth,
      sm: baseSmallWidth,
      md: baseSmallWidth,
      lg: baseSmallWidth,
    };

    if (field.sx) {
      return {
        width: {
          ...smallView,
          xl: field.sx,
        },
      };
    }

    const xlWidth =
      field.column === 1
        ? "26.563rem"
        : field.column === 2
        ? "25rem"
        : "18.75rem";

    return {
      width: {
        ...smallView,
        xl: xlWidth,
      },
    };
  },
};
