{"name": "uwe<PERSON>", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "generate": "rm -rf ./src/api/openapi && openapi --input uwekind-onboarding-api/uwekind-api.yaml --output ./src/api/openapi"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.1", "@fontsource/open-sans": "^5.2.6", "@mui/icons-material": "^7.2.0", "@mui/material": "^7.2.0", "@mui/x-data-grid": "^8.8.0", "i18next": "^25.3.1", "i18next-browser-languagedetector": "^8.2.0", "jwt-decode": "^4.0.0", "openapi-typescript-codegen": "^0.29.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-i18next": "^15.6.0", "react-router-dom": "^7.7.1", "virava": "^1.6.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@types/i18next": "^12.1.0", "@types/node": "^24.0.10", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@types/react-i18next": "^7.8.3", "@types/react-router-dom": "^5.3.3", "@typescript-eslint/eslint-plugin": "^8.35.1", "@typescript-eslint/parser": "^8.35.1", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.30.1", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-react": "^7.37.5", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.0"}}