{"name": "natcode-ui", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview", "lint": "eslint \"src/**/*.{ts,tsx,js,jsx}\"", "format": "prettier --write \"src/**/*.{ts,tsx,js,jsx,css,md,json}\"", "prepare": "husky", "generate-api": "npx @hey-api/openapi-ts --input natcode-api/natcode-api.yml --output src/lib/api --client @hey-api/client-fetch"}, "lint-staged": {"*.{ts,tsx}": ["prettier --write", "eslint --fix", "bash -c 'tsc --noEmit'"], "*.{js,jsx}": ["prettier --write", "eslint --fix"], "*.{json,md,css}": ["prettier --write"]}, "dependencies": {"@hey-api/openapi-ts": "^0.80.1", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-slot": "^1.2.3", "@tailwindcss/vite": "^4.1.11", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.534.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.7.1", "tailwind-merge": "^3.3.1", "virava": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.30.1", "@tailwindcss/postcss": "^4.1.11", "@types/node": "^24.1.0", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-config-prettier": "^10.1.8", "eslint-plugin-prettier": "^5.5.3", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "husky": "^9.1.7", "lint-staged": "^16.1.2", "postcss": "^8.5.6", "prettier": "^3.6.2", "tailwindcss": "^4.1.11", "tw-animate-css": "^1.3.6", "typescript": "~5.8.3", "typescript-eslint": "^8.35.1", "vite": "^7.0.4"}}